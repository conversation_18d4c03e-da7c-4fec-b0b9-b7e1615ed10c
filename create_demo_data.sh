#!/bin/bash

# Create comprehensive demo data for admin panel
BASE_URL="http://localhost:8080/api"

echo "🚀 Creating Comprehensive Demo Data"
echo "==================================="

# Get admin token (assuming admin user exists)
echo "📋 Getting Admin Token..."
ADMIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }')

ADMIN_TOKEN=$(echo $ADMIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Admin Token obtained: ${ADMIN_TOKEN:0:30}..."

# Create additional categories
echo "📋 Creating More Categories..."

curl -s -X POST "$BASE_URL/categories" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Sports & Fitness",
    "description": "Sports equipment and fitness gear"
  }' > /dev/null

curl -s -X POST "$BASE_URL/categories" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Books & Media",
    "description": "Books, magazines, and digital media"
  }' > /dev/null

curl -s -X POST "$BASE_URL/categories" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Beauty & Health",
    "description": "Beauty products and health supplements"
  }' > /dev/null

# Get category IDs
CATEGORIES_RESPONSE=$(curl -s -X GET "$BASE_URL/categories")
ELECTRONICS_ID=$(echo $CATEGORIES_RESPONSE | grep -o '"_id":"[^"]*","name":"Electronics"' | cut -d'"' -f4)
CLOTHING_ID=$(echo $CATEGORIES_RESPONSE | grep -o '"_id":"[^"]*","name":"Clothing"' | cut -d'"' -f4)
HOME_ID=$(echo $CATEGORIES_RESPONSE | grep -o '"_id":"[^"]*","name":"Home & Garden"' | cut -d'"' -f4)

echo "Category IDs obtained..."

# Create more products
echo "📋 Creating More Products..."

# Electronics Products
curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Wireless Headphones\",
    \"description\": \"Premium noise-cancelling wireless headphones\",
    \"price\": 12999,
    \"originalPrice\": 15999,
    \"category\": \"$ELECTRONICS_ID\",
    \"stock\": 30,
    \"isActive\": true
  }" > /dev/null

curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Smart Watch\",
    \"description\": \"Fitness tracking smartwatch with heart rate monitor\",
    \"price\": 25999,
    \"originalPrice\": 29999,
    \"category\": \"$ELECTRONICS_ID\",
    \"stock\": 15,
    \"isActive\": true
  }" > /dev/null

curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Laptop Stand\",
    \"description\": \"Adjustable aluminum laptop stand for better ergonomics\",
    \"price\": 3499,
    \"originalPrice\": 4999,
    \"category\": \"$ELECTRONICS_ID\",
    \"stock\": 45,
    \"isActive\": true
  }" > /dev/null

# Clothing Products
curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Denim Jeans\",
    \"description\": \"Classic blue denim jeans with comfortable fit\",
    \"price\": 2499,
    \"originalPrice\": 3499,
    \"category\": \"$CLOTHING_ID\",
    \"stock\": 60,
    \"isActive\": true
  }" > /dev/null

curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Winter Jacket\",
    \"description\": \"Warm winter jacket with waterproof material\",
    \"price\": 5999,
    \"originalPrice\": 7999,
    \"category\": \"$CLOTHING_ID\",
    \"stock\": 20,
    \"isActive\": true
  }" > /dev/null

curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Running Shoes\",
    \"description\": \"Lightweight running shoes with excellent cushioning\",
    \"price\": 4999,
    \"originalPrice\": 6999,
    \"category\": \"$CLOTHING_ID\",
    \"stock\": 35,
    \"isActive\": true
  }" > /dev/null

# Home & Garden Products
curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Coffee Maker\",
    \"description\": \"Automatic drip coffee maker with programmable timer\",
    \"price\": 8999,
    \"originalPrice\": 11999,
    \"category\": \"$HOME_ID\",
    \"stock\": 12,
    \"isActive\": true
  }" > /dev/null

curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Plant Pot Set\",
    \"description\": \"Set of 3 ceramic plant pots with drainage holes\",
    \"price\": 1999,
    \"originalPrice\": 2999,
    \"category\": \"$HOME_ID\",
    \"stock\": 40,
    \"isActive\": true
  }" > /dev/null

curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"LED Desk Lamp\",
    \"description\": \"Adjustable LED desk lamp with USB charging port\",
    \"price\": 3999,
    \"originalPrice\": 5499,
    \"category\": \"$HOME_ID\",
    \"stock\": 25,
    \"isActive\": true
  }" > /dev/null

# Create additional users
echo "📋 Creating Additional Users..."

curl -s -X POST "$BASE_URL/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "password": "user123",
    "role": "user"
  }' > /dev/null

curl -s -X POST "$BASE_URL/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Mike Johnson",
    "email": "<EMAIL>",
    "password": "user123",
    "role": "user"
  }' > /dev/null

curl -s -X POST "$BASE_URL/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Sarah Wilson",
    "email": "<EMAIL>",
    "password": "user123",
    "role": "user"
  }' > /dev/null

# Create additional coupons
echo "📋 Creating More Coupons..."

curl -s -X POST "$BASE_URL/coupons" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "SAVE10",
    "description": "Save 10% on orders above ₹1000",
    "discountType": "percentage",
    "discountValue": 10,
    "minimumOrderAmount": 1000,
    "validFrom": "2024-01-01T00:00:00.000Z",
    "validUntil": "2024-12-31T23:59:59.999Z",
    "usageLimit": 500,
    "isActive": true
  }' > /dev/null

curl -s -X POST "$BASE_URL/coupons" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "FLAT500",
    "description": "Flat ₹500 off on orders above ₹5000",
    "discountType": "fixed",
    "discountValue": 500,
    "minimumOrderAmount": 5000,
    "validFrom": "2024-01-01T00:00:00.000Z",
    "validUntil": "2024-12-31T23:59:59.999Z",
    "usageLimit": 200,
    "isActive": true
  }' > /dev/null

echo "📋 Final Statistics..."
curl -s -X GET "$BASE_URL/admin/stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

echo ""
echo ""
echo "🎉 Demo Data Creation Complete!"
echo "==============================="
echo ""
echo "📊 Created:"
echo "✅ 6 Categories"
echo "✅ 12 Products across different categories"
echo "✅ 5 Users (1 admin + 4 regular users)"
echo "✅ 3 Coupons with different discount types"
echo ""
echo "🔑 Admin Login:"
echo "Email: <EMAIL>"
echo "Password: admin123"
echo ""
echo "🌐 Admin Panel: http://localhost:3001"
echo "🔧 Backend API: http://localhost:8080"
echo ""
echo "Ready to test the admin panel!"
