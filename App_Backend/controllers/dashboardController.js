const Product = require('../models/Product');
const Category = require('../models/Category');
const Order = require('../models/Order');
const User = require('../models/User');

exports.getStats = async (req, res) => {
  try {
    const [productCount, categoryCount, orderCount, userCount, orders] = await Promise.all([
      Product.countDocuments(),
      Category.countDocuments(),
      Order.countDocuments(),
      User.countDocuments(),
      Order.find({}, 'total')
    ]);
    const totalSales = orders.reduce((sum, o) => sum + (o.total || 0), 0);
    res.json({
      productCount,
      categoryCount,
      orderCount,
      userCount,
      totalSales
    });
  } catch (err) {
    res.status(500).json({ error: 'Server error' });
  }
}; 