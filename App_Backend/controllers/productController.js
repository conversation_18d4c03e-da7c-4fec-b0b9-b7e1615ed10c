const Product = require('../models/Product');
const Category = require('../models/Category');

// Get all products (user)
exports.getProducts = async (req, res) => {
    try {
        const products = await Product.find({ isActive: true }).populate('category');
        const formatted = products.map(product => ({
            id: product._id,
            name: product.name,
            image: product.images && product.images.length > 0 ? product.images[0] : '',
            price: product.price,
            originalPrice: product.originalPrice || product.price,
            category: product.category ? product.category._id : null,
            rating: product.rating || 0,
            reviewCount: product.reviewCount || 0,
            isInStock: (product.stock > 0 || (product.variants && product.variants.some(v => v.stock > 0))),
            isFavorite: false // TODO: set true if in user's wishlist
        }));
        res.json(formatted);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Get product by ID (user)
exports.getProductById = async (req, res) => {
    try {
        const product = await Product.findById(req.params.id).populate('category');
        if (!product) return res.status(404).json({ error: 'Product not found' });
        res.json({
            id: product._id,
            name: product.name,
            image: product.images && product.images.length > 0 ? product.images[0] : '',
            price: product.price,
            originalPrice: product.originalPrice || product.price,
            category: product.category ? product.category._id : null,
            rating: product.rating || 0,
            reviewCount: product.reviewCount || 0,
            isInStock: (product.stock > 0 || (product.variants && product.variants.some(v => v.stock > 0))),
            isFavorite: false // TODO: set true if in user's wishlist
        });
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Create product (admin)
exports.createProduct = async (req, res) => {
    // TODO: Add admin check middleware
    try {
        const { name, description, price, originalPrice, category, stock, rating, reviewCount, isActive } = req.body;
        const images = req.files ? req.files.map(f => f.path) : [];
        const product = new Product({
            name, description, price, originalPrice, category, stock, rating, reviewCount, isActive, images
        });
        await product.save();
        res.status(201).json(product);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Update product (admin)
exports.updateProduct = async (req, res) => {
    // TODO: Add admin check middleware
    try {
        const updates = req.body;
        if (req.files) {
            updates.images = req.files.map(f => f.path);
        }
        const product = await Product.findByIdAndUpdate(req.params.id, updates, { new: true });
        if (!product) return res.status(404).json({ error: 'Product not found' });
        res.json(product);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Delete product (admin)
exports.deleteProduct = async (req, res) => {
    // TODO: Add admin check middleware
    try {
        const product = await Product.findByIdAndDelete(req.params.id);
        if (!product) return res.status(404).json({ error: 'Product not found' });
        res.json({ message: 'Product deleted' });
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Update inventory (admin)
exports.updateInventory = async (req, res) => {
    // TODO: Add admin check middleware
    try {
        const { stock } = req.body;
        const product = await Product.findByIdAndUpdate(req.params.id, { stock }, { new: true });
        if (!product) return res.status(404).json({ error: 'Product not found' });
        res.json(product);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
}; 