const Category = require('../models/Category');

// Create category (admin)
exports.createCategory = async (req, res) => {
    try {
        const { name, description, parent } = req.body;
        const image = req.file ? req.file.path : undefined;
        const category = new Category({ name, description, parent: parent || null, image });
        await category.save();
        res.status(201).json(category);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Get all categories
exports.getCategories = async (req, res) => {
    try {
        const categories = await Category.find().populate('parent');
        res.json(categories);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Update category (admin)
exports.updateCategory = async (req, res) => {
    try {
        const updates = req.body;
        if (req.file) {
            updates.image = req.file.path;
        }
        const category = await Category.findByIdAndUpdate(req.params.id, updates, { new: true });
        if (!category) return res.status(404).json({ error: 'Category not found' });
        res.json(category);
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
};

// Delete category (admin)
exports.deleteCategory = async (req, res) => {
    try {
        const category = await Category.findByIdAndDelete(req.params.id);
        if (!category) return res.status(404).json({ error: 'Category not found' });
        res.json({ message: 'Category deleted' });
    } catch (err) {
        res.status(500).json({ error: 'Server error' });
    }
}; 