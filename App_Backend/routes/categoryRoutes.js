const express = require('express');
const router = express.Router();
const categoryController = require('../controllers/categoryController');
const adminMiddleware = require('../middlewares/adminMiddleware');
const multer = require('multer');

const upload = multer({ dest: 'uploads/categories/' });

// Public endpoints
router.get('/', categoryController.getCategories);

// Admin endpoints
router.post('/', adminMiddleware, upload.single('image'), categoryController.createCategory);
router.put('/:id', adminMiddleware, upload.single('image'), categoryController.updateCategory);
router.delete('/:id', adminMiddleware, categoryController.deleteCategory);

module.exports = router; 