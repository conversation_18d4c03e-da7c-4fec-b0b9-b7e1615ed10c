const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const adminMiddleware = require('../middlewares/adminMiddleware');
const multer = require('multer');

const upload = multer({ dest: 'uploads/products/' });

// User endpoints
router.get('/', productController.getProducts);
router.get('/:id', productController.getProductById);

// Admin endpoints
router.post('/', adminMiddleware, upload.array('images'), productController.createProduct);
router.put('/:id', adminMiddleware, upload.array('images'), productController.updateProduct);
router.delete('/:id', adminMiddleware, productController.deleteProduct);
router.patch('/:id/inventory', adminMiddleware, productController.updateInventory);

module.exports = router; 