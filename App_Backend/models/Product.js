const mongoose = require('mongoose');

const variantSchema = new mongoose.Schema({
    name: String, // e.g., 'Size', 'Color'
    value: String, // e.g., 'M', 'Red'
    stock: { type: Number, default: 0 },
    price: Number // optional, for variant-specific pricing
});

const productSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    price: {
        type: Number,
        required: true
    },
    originalPrice: {
        type: Number
    },
    images: [{ type: String }], // file paths or URLs
    category: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Category',
        required: true
    },
    variants: [variantSchema],
    stock: {
        type: Number,
        default: 0
    },
    rating: {
        type: Number,
        default: 0
    },
    reviewCount: {
        type: Number,
        default: 0
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, { timestamps: true });

module.exports = mongoose.model('Product', productSchema);