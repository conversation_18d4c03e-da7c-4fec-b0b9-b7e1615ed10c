{"name": "app_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/deepak01112002/Ecommerce_backend_App.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/deepak01112002/Ecommerce_backend_App/issues"}, "homepage": "https://github.com/deepak01112002/Ecommerce_backend_App#readme", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "multer": "^2.0.1", "razorpay": "^2.9.4"}, "devDependencies": {"nodemon": "^3.1.10"}}