import { api } from './api';

// Helper function to extract data from standardized API response
const extractData = (response: any) => {
  if (response.success) {
    return response.data;
  } else {
    throw new Error(response.message || 'API request failed');
  }
};

// Auth Services
export const authService = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return extractData(response.data);
  },

  signup: async (name: string, email: string, password: string, role: string = 'admin') => {
    const response = await api.post('/auth/signup', { name, email, password, role });
    return extractData(response.data);
  },

  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return extractData(response.data);
  },

  updateProfile: async (data: any) => {
    const response = await api.put('/auth/profile', data);
    return extractData(response.data);
  }
};

// Dashboard Services
export const dashboardService = {
  getStats: async () => {
    const response = await api.get('/admin/stats');
    return extractData(response.data);
  }
};

// Product Services
export const productService = {
  getProducts: async (params?: any) => {
    const response = await api.get('/products', { params });
    const data = extractData(response.data);
    // Handle pagination format
    if (response.data.meta?.pagination) {
      return {
        products: data,
        pagination: response.data.meta.pagination
      };
    }
    return data;
  },

  getProductById: async (id: string) => {
    const response = await api.get(`/products/${id}`);
    return extractData(response.data);
  },

  createProduct: async (formData: FormData) => {
    const response = await api.post('/products', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return extractData(response.data);
  },

  updateProduct: async (id: string, formData: FormData) => {
    const response = await api.put(`/products/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return extractData(response.data);
  },

  deleteProduct: async (id: string) => {
    const response = await api.delete(`/products/${id}`);
    return extractData(response.data);
  },

  updateInventory: async (id: string, stock: number, operation: string = 'set') => {
    const response = await api.patch(`/products/${id}/inventory`, { stock, operation });
    return extractData(response.data);
  }
};

// Category Services
export const categoryService = {
  getCategories: async () => {
    const response = await api.get('/categories');
    return extractData(response.data);
  },

  createCategory: async (formData: FormData) => {
    const response = await api.post('/categories', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return extractData(response.data);
  },

  updateCategory: async (id: string, formData: FormData) => {
    const response = await api.put(`/categories/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return extractData(response.data);
  },

  deleteCategory: async (id: string) => {
    const response = await api.delete(`/categories/${id}`);
    return extractData(response.data);
  }
};

// Order Services
export const orderService = {
  getOrders: async (params?: any) => {
    const response = await api.get('/orders', { params });
    const data = extractData(response.data);
    // Handle pagination format
    if (response.data.meta?.pagination) {
      return {
        orders: data.orders || data,
        totalOrders: data.totalOrders || data.length,
        totalPages: response.data.meta.pagination.total_pages,
        currentPage: response.data.meta.pagination.current_page
      };
    }
    return data;
  },

  getOrderById: async (id: string) => {
    const response = await api.get(`/orders/${id}`);
    return extractData(response.data);
  },

  updateOrderStatus: async (id: string, status: string) => {
    const response = await api.patch(`/orders/${id}/status`, { status });
    return extractData(response.data);
  }
};

// User Services
export const userService = {
  getUsers: async (params?: any) => {
    const response = await api.get('/users', { params });
    return extractData(response.data);
  },

  getUserById: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return extractData(response.data);
  },

  updateUser: async (id: string, data: any) => {
    const response = await api.put(`/users/${id}`, data);
    return extractData(response.data);
  },

  deleteUser: async (id: string) => {
    const response = await api.delete(`/users/${id}`);
    return extractData(response.data);
  }
};

// Coupon Services
export const couponService = {
  getCoupons: async (params?: any) => {
    const response = await api.get('/coupons', { params });
    return extractData(response.data);
  },

  createCoupon: async (data: any) => {
    const response = await api.post('/coupons', data);
    return extractData(response.data);
  },

  updateCoupon: async (id: string, data: any) => {
    const response = await api.put(`/coupons/${id}`, data);
    return extractData(response.data);
  },

  deleteCoupon: async (id: string) => {
    const response = await api.delete(`/coupons/${id}`);
    return extractData(response.data);
  },

  validateCoupon: async (code: string, orderAmount: number, cartItems: any[]) => {
    const response = await api.post('/coupons/validate', { code, orderAmount, cartItems });
    return extractData(response.data);
  }
};

// Review Services
export const reviewService = {
  getProductReviews: async (productId: string, params?: any) => {
    const response = await api.get(`/reviews/product/${productId}`, { params });
    return extractData(response.data);
  },

  toggleReviewVisibility: async (reviewId: string) => {
    const response = await api.patch(`/reviews/${reviewId}/visibility`);
    return extractData(response.data);
  }
};

// Payment Services
export const paymentService = {
  getPaymentDetails: async (paymentId: string) => {
    const response = await api.get(`/payments/details/${paymentId}`);
    return extractData(response.data);
  },

  refundPayment: async (paymentId: string, amount: number, reason?: string) => {
    const response = await api.post('/payments/refund', { paymentId, amount, reason });
    return extractData(response.data);
  }
};
