import { api } from './api';

// Auth Services
export const authService = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },
  
  signup: async (name: string, email: string, password: string, role: string = 'admin') => {
    const response = await api.post('/auth/signup', { name, email, password, role });
    return response.data;
  },
  
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },
  
  updateProfile: async (data: any) => {
    const response = await api.put('/auth/profile', data);
    return response.data;
  }
};

// Dashboard Services
export const dashboardService = {
  getStats: async () => {
    const response = await api.get('/admin/stats');
    return response.data;
  }
};

// Product Services
export const productService = {
  getProducts: async (params?: any) => {
    const response = await api.get('/products', { params });
    return response.data;
  },
  
  getProductById: async (id: string) => {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },
  
  createProduct: async (formData: FormData) => {
    const response = await api.post('/products', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },
  
  updateProduct: async (id: string, formData: FormData) => {
    const response = await api.put(`/products/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },
  
  deleteProduct: async (id: string) => {
    const response = await api.delete(`/products/${id}`);
    return response.data;
  },
  
  updateInventory: async (id: string, stock: number) => {
    const response = await api.patch(`/products/${id}/inventory`, { stock });
    return response.data;
  }
};

// Category Services
export const categoryService = {
  getCategories: async () => {
    const response = await api.get('/categories');
    return response.data;
  },
  
  createCategory: async (formData: FormData) => {
    const response = await api.post('/categories', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },
  
  updateCategory: async (id: string, formData: FormData) => {
    const response = await api.put(`/categories/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },
  
  deleteCategory: async (id: string) => {
    const response = await api.delete(`/categories/${id}`);
    return response.data;
  }
};

// Order Services
export const orderService = {
  getOrders: async (params?: any) => {
    const response = await api.get('/orders', { params });
    return response.data;
  },
  
  getOrderById: async (id: string) => {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  },
  
  updateOrderStatus: async (id: string, status: string) => {
    const response = await api.put(`/orders/${id}`, { status });
    return response.data;
  }
};

// User Services
export const userService = {
  getUsers: async (params?: any) => {
    const response = await api.get('/users', { params });
    return response.data;
  },
  
  getUserById: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },
  
  updateUser: async (id: string, data: any) => {
    const response = await api.put(`/users/${id}`, data);
    return response.data;
  },
  
  deleteUser: async (id: string) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  }
};

// Coupon Services
export const couponService = {
  getCoupons: async (params?: any) => {
    const response = await api.get('/coupons', { params });
    return response.data;
  },
  
  createCoupon: async (data: any) => {
    const response = await api.post('/coupons', data);
    return response.data;
  },
  
  updateCoupon: async (id: string, data: any) => {
    const response = await api.put(`/coupons/${id}`, data);
    return response.data;
  },
  
  deleteCoupon: async (id: string) => {
    const response = await api.delete(`/coupons/${id}`);
    return response.data;
  },
  
  validateCoupon: async (code: string, orderAmount: number, cartItems: any[]) => {
    const response = await api.post('/coupons/validate', { code, orderAmount, cartItems });
    return response.data;
  }
};

// Review Services
export const reviewService = {
  getProductReviews: async (productId: string, params?: any) => {
    const response = await api.get(`/reviews/product/${productId}`, { params });
    return response.data;
  },
  
  toggleReviewVisibility: async (reviewId: string) => {
    const response = await api.patch(`/reviews/${reviewId}/visibility`);
    return response.data;
  }
};

// Payment Services
export const paymentService = {
  getPaymentDetails: async (paymentId: string) => {
    const response = await api.get(`/payments/details/${paymentId}`);
    return response.data;
  },
  
  refundPayment: async (paymentId: string, amount: number, reason?: string) => {
    const response = await api.post('/payments/refund', { paymentId, amount, reason });
    return response.data;
  }
};
