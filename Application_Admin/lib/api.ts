import axios from 'axios';

// Set this to your backend API URL
const API_URL = 'http://localhost:8080/api';

export const api = axios.create({
  baseURL: API_URL,
  withCredentials: false,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors and standardized responses
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/';
    }

    // Handle standardized error responses
    if (error.response?.data?.success === false) {
      const errorMessage = error.response.data.message || 'An error occurred';
      const errors = error.response.data.errors || [];

      // Create a more detailed error object
      const enhancedError = new Error(errorMessage);
      (enhancedError as any).errors = errors;
      (enhancedError as any).status = error.response.status;
      (enhancedError as any).response = error.response;

      return Promise.reject(enhancedError);
    }

    return Promise.reject(error);
  }
);

export function setAuthToken(token: string | null) {
  if (token) {
    localStorage.setItem('token', token);
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
  }
}