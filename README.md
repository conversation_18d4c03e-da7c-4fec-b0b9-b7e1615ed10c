# Ghanshyam Murti Bhandar - Complete Ecommerce Platform

A comprehensive ecommerce platform built with Node.js backend, Flutter mobile app, and Next.js admin panel.

## Project Structure

```
Application_ghansyam/
├── App_Backend/              # Node.js Backend API
├── ghanshyam_murti_bhandar/  # Flutter Mobile Application
├── Application_Admin/        # Next.js Admin Panel
└── README.md
```

## Features

### Backend API (Node.js + MongoDB)
- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Product Management**: CRUD operations with image upload, variants, inventory tracking
- **Category Management**: Hierarchical categories with images
- **Cart & Wishlist**: Support for both authenticated users and guests
- **Order Management**: Complete order lifecycle with status tracking
- **Payment Integration**: Razorpay payment gateway integration
- **Review System**: Product reviews with ratings and images
- **Coupon System**: Discount coupons with various conditions
- **User Management**: Profile management, addresses, password change
- **Admin Dashboard**: Statistics and management interfaces
- **File Upload**: Image handling for products, categories, and reviews

### Mobile App (Flutter)
- **User Authentication**: Login, signup, profile management
- **Product Browsing**: Categories, search, filters, product details
- **Shopping Cart**: Add to cart, quantity management, guest support
- **Wishlist**: Save favorite products
- **Order Management**: Place orders, track status, order history
- **Payment Integration**: Razorpay payment processing
- **Reviews**: View and add product reviews
- **Address Management**: Multiple delivery addresses
- **Responsive Design**: Optimized for mobile devices

### Admin Panel (Next.js)
- **Dashboard**: Real-time statistics and analytics
- **Product Management**: Add, edit, delete products with image upload
- **Category Management**: Organize product categories
- **Order Management**: View and update order status
- **User Management**: View and manage customers
- **Coupon Management**: Create and manage discount coupons
- **Review Moderation**: Hide/show product reviews
- **Inventory Tracking**: Stock management and alerts

## Technology Stack

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **File Upload**: Multer
- **Payment**: Razorpay
- **Validation**: Express Validator
- **Security**: Helmet, CORS, Rate Limiting

### Mobile App
- **Framework**: Flutter
- **Language**: Dart
- **State Management**: Provider/Bloc pattern
- **Navigation**: Go Router
- **HTTP Client**: Dio/HTTP
- **Local Storage**: Shared Preferences
- **Payment**: Razorpay Flutter SDK

### Admin Panel
- **Framework**: Next.js 14
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **HTTP Client**: Axios
- **Icons**: Lucide React

## Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Flutter SDK (for mobile app)
- Git

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd App_Backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the server**
   ```bash
   npm start
   # or for development
   npm run dev
   ```

5. **Server will run on**: `http://localhost:5000`

### Admin Panel Setup

1. **Navigate to admin directory**
   ```bash
   cd Application_Admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Admin panel will run on**: `http://localhost:3000`

### Mobile App Setup

1. **Navigate to Flutter app directory**
   ```bash
   cd ghanshyam_murti_bhandar
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

## API Documentation

Comprehensive API documentation is available at: `App_Backend/docs/API_DOCUMENTATION.md`

### Base URL
```
http://localhost:5000/api
```

### Key Endpoints
- **Authentication**: `/auth/login`, `/auth/signup`
- **Products**: `/products` (GET, POST, PUT, DELETE)
- **Categories**: `/categories`
- **Cart**: `/cart`
- **Orders**: `/orders`
- **Payments**: `/payments`
- **Admin**: `/admin/stats`

## Environment Variables

### Backend (.env)
```env
PORT=5000
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
```

### Admin Panel
Update `lib/api.ts` with your backend URL:
```typescript
const API_URL = 'http://localhost:5000/api';
```

## Default Admin Credentials

Create an admin user via API:
```bash
curl -X POST http://localhost:5000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Admin User",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin"
  }'
```

## Features Implementation Status

### ✅ Completed Features
- [x] Complete Backend API with all endpoints
- [x] Authentication & Authorization system
- [x] Product & Category management
- [x] Cart & Wishlist functionality
- [x] Order management system
- [x] Payment integration (Razorpay)
- [x] Review & Rating system
- [x] Coupon management
- [x] Admin panel with real API integration
- [x] File upload for images
- [x] User management
- [x] Dashboard statistics

### 🚧 In Progress
- [ ] Flutter app API integration
- [ ] Push notifications
- [ ] Email notifications
- [ ] Advanced analytics

### 📋 Future Enhancements
- [ ] Multi-vendor support
- [ ] Advanced search with Elasticsearch
- [ ] Real-time chat support
- [ ] Social media integration
- [ ] PWA for web version

## Project Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │  Admin Panel    │    │   Backend API   │
│   (Mobile)      │◄──►│   (Next.js)     │◄──►│   (Node.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │    MongoDB      │
                                               │   (Database)    │
                                               └─────────────────┘
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue in the repository
- Email: <EMAIL>

## Deployment

### Backend Deployment
- Deploy to services like Heroku, DigitalOcean, or AWS
- Set up MongoDB Atlas for cloud database
- Configure environment variables

### Admin Panel Deployment
- Deploy to Vercel, Netlify, or similar platforms
- Update API URLs for production

### Mobile App Deployment
- Build APK/IPA files
- Deploy to Google Play Store / Apple App Store

---

**Built with ❤️ for Ghanshyam Murti Bhandar**
