# Ghanshyam Murti Bhandar - Complete API Documentation

## Base URL
```
Production: https://api.ghanshyammurtibhandar.com/v1
Development: https://dev-api.ghanshyammurtibhandar.com/v1
```

## Authentication
All authenticated endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Common Headers
```
Content-Type: application/json
Accept: application/json
X-API-Version: 1.0
X-Platform: mobile|web
X-App-Version: 1.0.0
```

## Response Format
All responses follow this structure:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {},
  "errors": [],
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
  }
}
```

## Error Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 429: Rate Limit Exceeded
- 500: Internal Server Error

---

# 1. AUTHENTICATION ENDPOINTS

## 1.1 User Registration
**POST** `/auth/register`

### Request Body
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "SecurePass123!",
  "password_confirmation": "SecurePass123!",
  "device_token": "fcm_device_token_here",
  "platform": "android|ios|web"
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "email_verified_at": null,
      "phone_verified_at": null,
      "avatar": null,
      "created_at": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "access_token": "jwt_access_token",
      "refresh_token": "jwt_refresh_token",
      "expires_in": 3600
    }
  }
}
```

## 1.2 User Login
**POST** `/auth/login`

### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "device_token": "fcm_device_token_here",
  "platform": "android|ios|web"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "email_verified_at": "2024-01-01T00:00:00Z",
      "phone_verified_at": "2024-01-01T00:00:00Z",
      "avatar": "https://example.com/avatar.jpg",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "access_token": "jwt_access_token",
      "refresh_token": "jwt_refresh_token",
      "expires_in": 3600
    }
  }
}
```

## 1.3 Refresh Token
**POST** `/auth/refresh`

### Request Body
```json
{
  "refresh_token": "jwt_refresh_token"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "access_token": "new_jwt_access_token",
    "refresh_token": "new_jwt_refresh_token",
    "expires_in": 3600
  }
}
```

## 1.4 Logout
**POST** `/auth/logout`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Logged out successfully",
  "data": null
}
```

## 1.5 Forgot Password
**POST** `/auth/forgot-password`

### Request Body
```json
{
  "email": "<EMAIL>"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Password reset link sent to your email",
  "data": null
}
```

## 1.6 Reset Password
**POST** `/auth/reset-password`

### Request Body
```json
{
  "email": "<EMAIL>",
  "token": "reset_token_from_email",
  "password": "NewSecurePass123!",
  "password_confirmation": "NewSecurePass123!"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Password reset successfully",
  "data": null
}
```

## 1.7 Send OTP
**POST** `/auth/send-otp`

### Request Body
```json
{
  "phone": "+************",
  "type": "registration|login|verification"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "otp_id": "uuid",
    "expires_in": 300
  }
}
```

## 1.8 Verify OTP
**POST** `/auth/verify-otp`

### Request Body
```json
{
  "phone": "+************",
  "otp": "123456",
  "otp_id": "uuid"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "verified": true
  }
}
```

---

# 2. USER PROFILE ENDPOINTS

## 2.1 Get User Profile
**GET** `/user/profile`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "avatar": "https://example.com/avatar.jpg",
      "date_of_birth": "1990-01-01",
      "gender": "male|female|other",
      "email_verified_at": "2024-01-01T00:00:00Z",
      "phone_verified_at": "2024-01-01T00:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 2.2 Update User Profile
**PUT** `/user/profile`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "name": "John Doe Updated",
  "phone": "+************",
  "date_of_birth": "1990-01-01",
  "gender": "male"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe Updated",
      "email": "<EMAIL>",
      "phone": "+************",
      "avatar": "https://example.com/avatar.jpg",
      "date_of_birth": "1990-01-01",
      "gender": "male",
      "email_verified_at": "2024-01-01T00:00:00Z",
      "phone_verified_at": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 2.3 Upload Avatar
**POST** `/user/avatar`
**Headers:** Authorization: Bearer <token>, Content-Type: multipart/form-data

### Request Body (Form Data)
```
avatar: <image_file>
```

### Response (200)
```json
{
  "success": true,
  "message": "Avatar uploaded successfully",
  "data": {
    "avatar_url": "https://example.com/new-avatar.jpg"
  }
}
```

## 2.4 Change Password
**POST** `/user/change-password`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "current_password": "OldPassword123!",
  "new_password": "NewPassword123!",
  "new_password_confirmation": "NewPassword123!"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Password changed successfully",
  "data": null
}
```

---

# 3. PRODUCT ENDPOINTS

## 3.1 Get All Products
**GET** `/products`

### Query Parameters
```
page=1
per_page=20
category_id=1
search=murti
sort_by=name|price|created_at|popularity
sort_order=asc|desc
min_price=100
max_price=5000
in_stock=true|false
featured=true|false
```

### Response (200)
```json
{
  "success": true,
  "message": "Products retrieved successfully",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "Ganesha Murti",
        "slug": "ganesha-murti",
        "description": "Beautiful handcrafted Ganesha murti",
        "short_description": "Handcrafted Ganesha murti",
        "sku": "GM001",
        "price": 1500.00,
        "sale_price": 1200.00,
        "currency": "INR",
        "stock_quantity": 50,
        "in_stock": true,
        "featured": true,
        "weight": "2.5",
        "dimensions": {
          "length": "15",
          "width": "10",
          "height": "20"
        },
        "images": [
          {
            "id": 1,
            "url": "https://example.com/image1.jpg",
            "alt": "Ganesha Murti Front View",
            "is_primary": true
          }
        ],
        "category": {
          "id": 1,
          "name": "Ganesha Murtis",
          "slug": "ganesha-murtis"
        },
        "attributes": [
          {
            "name": "Material",
            "value": "Marble"
          },
          {
            "name": "Color",
            "value": "White"
          }
        ],
        "rating": {
          "average": 4.5,
          "count": 25
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## 3.2 Get Single Product
**GET** `/products/{id}`

### Response (200)
```json
{
  "success": true,
  "message": "Product retrieved successfully",
  "data": {
    "product": {
      "id": 1,
      "name": "Ganesha Murti",
      "slug": "ganesha-murti",
      "description": "Beautiful handcrafted Ganesha murti made with premium marble...",
      "short_description": "Handcrafted Ganesha murti",
      "sku": "GM001",
      "price": 1500.00,
      "sale_price": 1200.00,
      "currency": "INR",
      "stock_quantity": 50,
      "in_stock": true,
      "featured": true,
      "weight": "2.5",
      "dimensions": {
        "length": "15",
        "width": "10",
        "height": "20"
      },
      "images": [
        {
          "id": 1,
          "url": "https://example.com/image1.jpg",
          "alt": "Ganesha Murti Front View",
          "is_primary": true
        },
        {
          "id": 2,
          "url": "https://example.com/image2.jpg",
          "alt": "Ganesha Murti Side View",
          "is_primary": false
        }
      ],
      "category": {
        "id": 1,
        "name": "Ganesha Murtis",
        "slug": "ganesha-murtis",
        "parent_id": null
      },
      "attributes": [
        {
          "name": "Material",
          "value": "Marble"
        },
        {
          "name": "Color",
          "value": "White"
        },
        {
          "name": "Finish",
          "value": "Polished"
        }
      ],
      "variants": [
        {
          "id": 1,
          "name": "Small",
          "price": 1200.00,
          "sale_price": 1000.00,
          "sku": "GM001-S",
          "stock_quantity": 20
        },
        {
          "id": 2,
          "name": "Medium",
          "price": 1500.00,
          "sale_price": 1200.00,
          "sku": "GM001-M",
          "stock_quantity": 30
        }
      ],
      "rating": {
        "average": 4.5,
        "count": 25,
        "breakdown": {
          "5": 15,
          "4": 8,
          "3": 2,
          "2": 0,
          "1": 0
        }
      },
      "reviews": [
        {
          "id": 1,
          "user_name": "Priya Sharma",
          "rating": 5,
          "comment": "Beautiful murti, excellent quality!",
          "created_at": "2024-01-01T00:00:00Z"
        }
      ],
      "related_products": [
        {
          "id": 2,
          "name": "Lakshmi Murti",
          "price": 1800.00,
          "sale_price": 1500.00,
          "image": "https://example.com/lakshmi.jpg"
        }
      ],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 3.3 Get Featured Products
**GET** `/products/featured`

### Query Parameters
```
limit=10
category_id=1
```

### Response (200)
```json
{
  "success": true,
  "message": "Featured products retrieved successfully",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "Ganesha Murti",
        "slug": "ganesha-murti",
        "price": 1500.00,
        "sale_price": 1200.00,
        "currency": "INR",
        "image": "https://example.com/image1.jpg",
        "rating": {
          "average": 4.5,
          "count": 25
        },
        "in_stock": true
      }
    ]
  }
}
```

## 3.4 Search Products
**GET** `/products/search`

### Query Parameters
```
q=ganesha murti
category_id=1
subcategory_id=2
min_price=100
max_price=5000
material=marble|brass|stone|wood
color=white|black|golden|silver
size=small|medium|large|extra_large
weight_min=1.0
weight_max=10.0
in_stock=true|false
featured=true|false
on_sale=true|false
rating_min=4
sort_by=relevance|name|price|rating|popularity|newest|oldest
sort_order=asc|desc
page=1
per_page=20
```

### Response (200)
```json
{
  "success": true,
  "message": "Search results retrieved successfully",
  "data": {
    "query": "ganesha murti",
    "total_results": 45,
    "search_time": "0.23 seconds",
    "products": [
      {
        "id": 1,
        "name": "Premium Marble Ganesha Murti",
        "slug": "premium-marble-ganesha-murti",
        "description": "Exquisite handcrafted marble Ganesha murti",
        "short_description": "Premium marble Ganesha",
        "sku": "GM001",
        "price": 2500.00,
        "sale_price": 2000.00,
        "discount_percentage": 20,
        "currency": "INR",
        "stock_quantity": 25,
        "in_stock": true,
        "featured": true,
        "on_sale": true,
        "weight": "3.5",
        "dimensions": {
          "length": "20",
          "width": "15",
          "height": "25"
        },
        "images": [
          {
            "id": 1,
            "url": "https://example.com/ganesha1.jpg",
            "alt": "Premium Marble Ganesha Front",
            "is_primary": true
          },
          {
            "id": 2,
            "url": "https://example.com/ganesha1-side.jpg",
            "alt": "Premium Marble Ganesha Side",
            "is_primary": false
          }
        ],
        "category": {
          "id": 1,
          "name": "Ganesha Murtis",
          "slug": "ganesha-murtis"
        },
        "subcategory": {
          "id": 2,
          "name": "Marble Ganesha",
          "slug": "marble-ganesha"
        },
        "attributes": [
          {
            "name": "Material",
            "value": "Marble",
            "slug": "marble"
          },
          {
            "name": "Color",
            "value": "White",
            "slug": "white"
          },
          {
            "name": "Size",
            "value": "Medium",
            "slug": "medium"
          },
          {
            "name": "Finish",
            "value": "Polished",
            "slug": "polished"
          },
          {
            "name": "Origin",
            "value": "Rajasthan",
            "slug": "rajasthan"
          }
        ],
        "rating": {
          "average": 4.8,
          "count": 156,
          "breakdown": {
            "5": 120,
            "4": 25,
            "3": 8,
            "2": 2,
            "1": 1
          }
        },
        "tags": ["handcrafted", "premium", "marble", "ganesha", "religious"],
        "is_wishlisted": false,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "suggestions": [
      "ganesha marble murti",
      "ganesha brass murti",
      "small ganesha murti",
      "ganesha murti for home",
      "ganesha murti online",
      "handcrafted ganesha murti"
    ],
    "related_searches": [
      "lakshmi murti",
      "krishna murti",
      "hanuman murti",
      "shiva murti"
    ],
    "filters": {
      "applied_filters": [
        {
          "type": "category",
          "value": "Ganesha Murtis",
          "count": 45
        }
      ],
      "available_filters": {
        "categories": [
          {
            "id": 1,
            "name": "Ganesha Murtis",
            "slug": "ganesha-murtis",
            "count": 45,
            "selected": true
          },
          {
            "id": 2,
            "name": "Lakshmi Murtis",
            "slug": "lakshmi-murtis",
            "count": 12,
            "selected": false
          }
        ],
        "subcategories": [
          {
            "id": 2,
            "name": "Marble Ganesha",
            "slug": "marble-ganesha",
            "count": 25,
            "selected": false
          },
          {
            "id": 3,
            "name": "Brass Ganesha",
            "slug": "brass-ganesha",
            "count": 20,
            "selected": false
          }
        ],
        "price_ranges": [
          {
            "min": 0,
            "max": 1000,
            "label": "Under ₹1,000",
            "count": 8,
            "selected": false
          },
          {
            "min": 1000,
            "max": 2500,
            "label": "₹1,000 - ₹2,500",
            "count": 22,
            "selected": false
          },
          {
            "min": 2500,
            "max": 5000,
            "label": "₹2,500 - ₹5,000",
            "count": 12,
            "selected": false
          },
          {
            "min": 5000,
            "max": null,
            "label": "Above ₹5,000",
            "count": 3,
            "selected": false
          }
        ],
        "materials": [
          {
            "name": "Marble",
            "slug": "marble",
            "count": 25,
            "selected": false
          },
          {
            "name": "Brass",
            "slug": "brass",
            "count": 20,
            "selected": false
          },
          {
            "name": "Stone",
            "slug": "stone",
            "count": 15,
            "selected": false
          },
          {
            "name": "Wood",
            "slug": "wood",
            "count": 8,
            "selected": false
          }
        ],
        "colors": [
          {
            "name": "White",
            "slug": "white",
            "hex": "#FFFFFF",
            "count": 30,
            "selected": false
          },
          {
            "name": "Golden",
            "slug": "golden",
            "hex": "#FFD700",
            "count": 18,
            "selected": false
          },
          {
            "name": "Black",
            "slug": "black",
            "hex": "#000000",
            "count": 12,
            "selected": false
          }
        ],
        "sizes": [
          {
            "name": "Small",
            "slug": "small",
            "description": "Up to 15cm",
            "count": 15,
            "selected": false
          },
          {
            "name": "Medium",
            "slug": "medium",
            "description": "15-25cm",
            "count": 20,
            "selected": false
          },
          {
            "name": "Large",
            "slug": "large",
            "description": "25-40cm",
            "count": 8,
            "selected": false
          },
          {
            "name": "Extra Large",
            "slug": "extra_large",
            "description": "Above 40cm",
            "count": 2,
            "selected": false
          }
        ],
        "ratings": [
          {
            "rating": 5,
            "count": 25,
            "selected": false
          },
          {
            "rating": 4,
            "count": 35,
            "selected": false
          },
          {
            "rating": 3,
            "count": 10,
            "selected": false
          }
        ],
        "availability": [
          {
            "type": "in_stock",
            "label": "In Stock",
            "count": 42,
            "selected": false
          },
          {
            "type": "featured",
            "label": "Featured",
            "count": 15,
            "selected": false
          },
          {
            "type": "on_sale",
            "label": "On Sale",
            "count": 18,
            "selected": false
          }
        ]
      }
    },
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 45,
      "total_pages": 3,
      "has_next": true,
      "has_prev": false,
      "next_page": 2,
      "prev_page": null
    },
    "meta": {
      "search_id": "search_uuid_123",
      "cached": false,
      "search_suggestions_enabled": true
    }
  }
}
```

## 3.5 Advanced Search with Filters
**POST** `/products/search/advanced`

### Request Body
```json
{
  "query": "ganesha murti",
  "filters": {
    "category_ids": [1, 2],
    "subcategory_ids": [2, 3],
    "price_range": {
      "min": 1000,
      "max": 5000
    },
    "materials": ["marble", "brass"],
    "colors": ["white", "golden"],
    "sizes": ["medium", "large"],
    "weight_range": {
      "min": 2.0,
      "max": 8.0
    },
    "rating_min": 4,
    "in_stock": true,
    "featured": false,
    "on_sale": true,
    "tags": ["handcrafted", "premium"]
  },
  "sort": {
    "by": "popularity",
    "order": "desc"
  },
  "pagination": {
    "page": 1,
    "per_page": 20
  }
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Advanced search completed successfully",
  "data": {
    "products": [],
    "total_results": 25,
    "applied_filters_count": 8,
    "search_analytics": {
      "search_id": "search_uuid_456",
      "execution_time": "0.18 seconds",
      "cache_hit": false
    },
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 25,
      "total_pages": 2
    }
  }
}
```

## 3.6 Search Autocomplete
**GET** `/search/autocomplete`

### Query Parameters
```
q=gane
limit=10
include_categories=true
include_products=true
```

### Response (200)
```json
{
  "success": true,
  "message": "Autocomplete suggestions retrieved successfully",
  "data": {
    "suggestions": {
      "queries": [
        {
          "text": "ganesha murti",
          "type": "query",
          "popularity": 95,
          "results_count": 45
        },
        {
          "text": "ganesha marble",
          "type": "query",
          "popularity": 78,
          "results_count": 25
        },
        {
          "text": "ganesha brass",
          "type": "query",
          "popularity": 65,
          "results_count": 20
        }
      ],
      "categories": [
        {
          "id": 1,
          "name": "Ganesha Murtis",
          "slug": "ganesha-murtis",
          "type": "category",
          "products_count": 45,
          "image": "https://example.com/category-ganesha.jpg"
        }
      ],
      "products": [
        {
          "id": 1,
          "name": "Premium Marble Ganesha Murti",
          "slug": "premium-marble-ganesha-murti",
          "type": "product",
          "price": 2000.00,
          "image": "https://example.com/ganesha1.jpg",
          "rating": 4.8
        }
      ]
    },
    "meta": {
      "query": "gane",
      "total_suggestions": 7,
      "response_time": "0.05 seconds"
    }
  }
}
```

## 3.7 Search History
**GET** `/search/history`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
limit=20
```

### Response (200)
```json
{
  "success": true,
  "message": "Search history retrieved successfully",
  "data": {
    "searches": [
      {
        "id": 1,
        "query": "ganesha marble murti",
        "results_count": 25,
        "clicked_product_id": 1,
        "searched_at": "2024-01-01T10:30:00Z"
      },
      {
        "id": 2,
        "query": "lakshmi murti brass",
        "results_count": 15,
        "clicked_product_id": null,
        "searched_at": "2024-01-01T09:15:00Z"
      }
    ],
    "popular_searches": [
      {
        "query": "ganesha murti",
        "search_count": 1250
      },
      {
        "query": "lakshmi murti",
        "search_count": 890
      }
    ]
  }
}
```

## 3.8 Clear Search History
**DELETE** `/search/history`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Search history cleared successfully",
  "data": null
}
```

## 3.9 Save Search
**POST** `/search/save`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "query": "ganesha marble murti",
  "filters": {
    "category_id": 1,
    "price_range": {
      "min": 1000,
      "max": 3000
    },
    "material": "marble"
  },
  "name": "My Ganesha Search"
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Search saved successfully",
  "data": {
    "saved_search": {
      "id": 1,
      "name": "My Ganesha Search",
      "query": "ganesha marble murti",
      "filters": {
        "category_id": 1,
        "price_range": {
          "min": 1000,
          "max": 3000
        },
        "material": "marble"
      },
      "results_count": 25,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 3.10 Get Saved Searches
**GET** `/search/saved`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Saved searches retrieved successfully",
  "data": {
    "saved_searches": [
      {
        "id": 1,
        "name": "My Ganesha Search",
        "query": "ganesha marble murti",
        "results_count": 25,
        "last_checked": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

---

# 4. CATEGORY ENDPOINTS

## 4.1 Get All Categories
**GET** `/categories`

### Query Parameters
```
parent_id=null|1
include_products=true|false
include_children=true|false
include_ancestors=true|false
level=1|2|3
featured=true|false
active=true|false
limit=50
sort_by=name|sort_order|products_count|created_at
sort_order=asc|desc
```

### Response (200)
```json
{
  "success": true,
  "message": "Categories retrieved successfully",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Ganesha Murtis",
        "slug": "ganesha-murtis",
        "description": "Beautiful collection of handcrafted Ganesha murtis in various materials and sizes",
        "short_description": "Handcrafted Ganesha collection",
        "image": "https://example.com/category-ganesha.jpg",
        "banner_image": "https://example.com/banner-ganesha.jpg",
        "icon": "https://example.com/icon-ganesha.svg",
        "parent_id": null,
        "level": 1,
        "sort_order": 1,
        "is_active": true,
        "is_featured": true,
        "products_count": 45,
        "meta_title": "Ganesha Murtis - Handcrafted Religious Idols",
        "meta_description": "Shop beautiful handcrafted Ganesha murtis online...",
        "attributes": [
          {
            "name": "Material",
            "values": ["Marble", "Brass", "Stone", "Wood"],
            "filterable": true
          },
          {
            "name": "Size",
            "values": ["Small", "Medium", "Large", "Extra Large"],
            "filterable": true
          }
        ],
        "price_range": {
          "min": 500.00,
          "max": 15000.00,
          "currency": "INR"
        },
        "children": [
          {
            "id": 2,
            "name": "Marble Ganesha",
            "slug": "marble-ganesha",
            "description": "Premium marble Ganesha murtis",
            "image": "https://example.com/marble-ganesha.jpg",
            "parent_id": 1,
            "level": 2,
            "sort_order": 1,
            "is_active": true,
            "is_featured": true,
            "products_count": 25,
            "price_range": {
              "min": 1500.00,
              "max": 15000.00
            }
          },
          {
            "id": 3,
            "name": "Brass Ganesha",
            "slug": "brass-ganesha",
            "description": "Traditional brass Ganesha murtis",
            "image": "https://example.com/brass-ganesha.jpg",
            "parent_id": 1,
            "level": 2,
            "sort_order": 2,
            "is_active": true,
            "is_featured": false,
            "products_count": 20,
            "price_range": {
              "min": 800.00,
              "max": 8000.00
            }
          }
        ],
        "ancestors": [],
        "breadcrumb": [
          {
            "id": 1,
            "name": "Ganesha Murtis",
            "slug": "ganesha-murtis"
          }
        ],
        "seo": {
          "canonical_url": "https://ghanshyammurtibhandar.com/categories/ganesha-murtis",
          "og_title": "Ganesha Murtis - Handcrafted Collection",
          "og_description": "Shop beautiful handcrafted Ganesha murtis...",
          "og_image": "https://example.com/og-ganesha.jpg"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": 4,
        "name": "Lakshmi Murtis",
        "slug": "lakshmi-murtis",
        "description": "Divine Lakshmi murtis for prosperity and wealth",
        "image": "https://example.com/category-lakshmi.jpg",
        "parent_id": null,
        "level": 1,
        "sort_order": 2,
        "is_active": true,
        "is_featured": true,
        "products_count": 32,
        "children": [
          {
            "id": 5,
            "name": "Marble Lakshmi",
            "slug": "marble-lakshmi",
            "products_count": 18
          },
          {
            "id": 6,
            "name": "Brass Lakshmi",
            "slug": "brass-lakshmi",
            "products_count": 14
          }
        ]
      }
    ],
    "meta": {
      "total_categories": 15,
      "featured_count": 8,
      "hierarchy_levels": 3
    }
  }
}
```

## 4.2 Get Single Category
**GET** `/categories/{id}`

### Query Parameters
```
include_products=true|false
include_children=true|false
include_ancestors=true|false
include_filters=true|false
products_limit=20
products_sort_by=name|price|popularity|newest
```

### Response (200)
```json
{
  "success": true,
  "message": "Category retrieved successfully",
  "data": {
    "category": {
      "id": 1,
      "name": "Ganesha Murtis",
      "slug": "ganesha-murtis",
      "description": "Beautiful collection of handcrafted Ganesha murtis in various materials and sizes. Each piece is crafted with devotion and attention to detail by skilled artisans.",
      "short_description": "Handcrafted Ganesha collection",
      "image": "https://example.com/category-ganesha.jpg",
      "banner_image": "https://example.com/banner-ganesha.jpg",
      "icon": "https://example.com/icon-ganesha.svg",
      "gallery": [
        {
          "id": 1,
          "url": "https://example.com/gallery1.jpg",
          "alt": "Ganesha Collection 1"
        },
        {
          "id": 2,
          "url": "https://example.com/gallery2.jpg",
          "alt": "Ganesha Collection 2"
        }
      ],
      "parent_id": null,
      "level": 1,
      "sort_order": 1,
      "is_active": true,
      "is_featured": true,
      "products_count": 45,
      "meta_title": "Ganesha Murtis - Handcrafted Religious Idols",
      "meta_description": "Shop beautiful handcrafted Ganesha murtis online. Premium quality marble, brass, and stone Ganesha idols for home and temple.",
      "meta_keywords": ["ganesha murti", "handcrafted", "religious idols", "marble ganesha"],
      "attributes": [
        {
          "name": "Material",
          "slug": "material",
          "values": [
            {
              "value": "Marble",
              "slug": "marble",
              "count": 25,
              "image": "https://example.com/material-marble.jpg"
            },
            {
              "value": "Brass",
              "slug": "brass",
              "count": 20,
              "image": "https://example.com/material-brass.jpg"
            }
          ],
          "filterable": true,
          "display_type": "grid"
        },
        {
          "name": "Size",
          "slug": "size",
          "values": [
            {
              "value": "Small",
              "slug": "small",
              "count": 15,
              "description": "Up to 15cm"
            },
            {
              "value": "Medium",
              "slug": "medium",
              "count": 20,
              "description": "15-25cm"
            }
          ],
          "filterable": true,
          "display_type": "list"
        }
      ],
      "price_range": {
        "min": 500.00,
        "max": 15000.00,
        "currency": "INR",
        "average": 3500.00
      },
      "children": [
        {
          "id": 2,
          "name": "Marble Ganesha",
          "slug": "marble-ganesha",
          "description": "Premium marble Ganesha murtis",
          "image": "https://example.com/marble-ganesha.jpg",
          "parent_id": 1,
          "level": 2,
          "sort_order": 1,
          "is_active": true,
          "is_featured": true,
          "products_count": 25,
          "price_range": {
            "min": 1500.00,
            "max": 15000.00
          }
        }
      ],
      "ancestors": [],
      "breadcrumb": [
        {
          "id": 1,
          "name": "Ganesha Murtis",
          "slug": "ganesha-murtis",
          "url": "/categories/ganesha-murtis"
        }
      ],
      "featured_products": [
        {
          "id": 1,
          "name": "Premium Marble Ganesha",
          "slug": "premium-marble-ganesha",
          "price": 2500.00,
          "sale_price": 2000.00,
          "image": "https://example.com/product1.jpg",
          "rating": 4.8,
          "in_stock": true
        }
      ],
      "related_categories": [
        {
          "id": 4,
          "name": "Lakshmi Murtis",
          "slug": "lakshmi-murtis",
          "image": "https://example.com/lakshmi.jpg",
          "products_count": 32
        }
      ],
      "seo": {
        "canonical_url": "https://ghanshyammurtibhandar.com/categories/ganesha-murtis",
        "og_title": "Ganesha Murtis - Handcrafted Collection",
        "og_description": "Shop beautiful handcrafted Ganesha murtis...",
        "og_image": "https://example.com/og-ganesha.jpg",
        "structured_data": {
          "@type": "ProductCategory",
          "name": "Ganesha Murtis",
          "description": "Beautiful collection of handcrafted Ganesha murtis..."
        }
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 4.3 Get Category Tree
**GET** `/categories/tree`

### Query Parameters
```
max_depth=3
include_products_count=true
active_only=true
```

### Response (200)
```json
{
  "success": true,
  "message": "Category tree retrieved successfully",
  "data": {
    "tree": [
      {
        "id": 1,
        "name": "Ganesha Murtis",
        "slug": "ganesha-murtis",
        "level": 1,
        "products_count": 45,
        "children": [
          {
            "id": 2,
            "name": "Marble Ganesha",
            "slug": "marble-ganesha",
            "level": 2,
            "products_count": 25,
            "children": [
              {
                "id": 7,
                "name": "Small Marble Ganesha",
                "slug": "small-marble-ganesha",
                "level": 3,
                "products_count": 12,
                "children": []
              }
            ]
          },
          {
            "id": 3,
            "name": "Brass Ganesha",
            "slug": "brass-ganesha",
            "level": 2,
            "products_count": 20,
            "children": []
          }
        ]
      }
    ],
    "meta": {
      "max_depth": 3,
      "total_categories": 15,
      "total_products": 150
    }
  }
}
```

## 4.4 Get Featured Categories
**GET** `/categories/featured`

### Query Parameters
```
limit=10
include_products=true
```

### Response (200)
```json
{
  "success": true,
  "message": "Featured categories retrieved successfully",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Ganesha Murtis",
        "slug": "ganesha-murtis",
        "image": "https://example.com/category-ganesha.jpg",
        "products_count": 45,
        "featured_products": [
          {
            "id": 1,
            "name": "Premium Marble Ganesha",
            "price": 2000.00,
            "image": "https://example.com/product1.jpg"
          }
        ]
      }
    ]
  }
}
```

---

# 5. CART ENDPOINTS

## 5.1 Get Cart
**GET** `/cart`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Cart retrieved successfully",
  "data": {
    "cart": {
      "id": 1,
      "user_id": 1,
      "items": [
        {
          "id": 1,
          "product_id": 1,
          "product_variant_id": 2,
          "quantity": 2,
          "price": 1200.00,
          "total": 2400.00,
          "product": {
            "id": 1,
            "name": "Ganesha Murti",
            "slug": "ganesha-murti",
            "image": "https://example.com/image1.jpg",
            "in_stock": true,
            "stock_quantity": 50
          },
          "variant": {
            "id": 2,
            "name": "Medium",
            "sku": "GM001-M"
          }
        }
      ],
      "subtotal": 2400.00,
      "tax": 240.00,
      "shipping": 100.00,
      "discount": 0.00,
      "total": 2740.00,
      "currency": "INR",
      "items_count": 2,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 5.2 Add to Cart
**POST** `/cart/add`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "product_id": 1,
  "product_variant_id": 2,
  "quantity": 1
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Product added to cart successfully",
  "data": {
    "cart_item": {
      "id": 1,
      "product_id": 1,
      "product_variant_id": 2,
      "quantity": 1,
      "price": 1200.00,
      "total": 1200.00,
      "product": {
        "id": 1,
        "name": "Ganesha Murti",
        "image": "https://example.com/image1.jpg"
      }
    },
    "cart_summary": {
      "items_count": 1,
      "subtotal": 1200.00,
      "total": 1340.00
    }
  }
}
```

## 5.3 Update Cart Item
**PUT** `/cart/items/{item_id}`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "quantity": 3
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Cart item updated successfully",
  "data": {
    "cart_item": {
      "id": 1,
      "quantity": 3,
      "price": 1200.00,
      "total": 3600.00
    },
    "cart_summary": {
      "items_count": 3,
      "subtotal": 3600.00,
      "total": 3940.00
    }
  }
}
```

## 5.4 Remove from Cart
**DELETE** `/cart/items/{item_id}`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Item removed from cart successfully",
  "data": {
    "cart_summary": {
      "items_count": 0,
      "subtotal": 0.00,
      "total": 0.00
    }
  }
}
```

## 5.5 Clear Cart
**DELETE** `/cart/clear`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Cart cleared successfully",
  "data": null
}
```

## 5.6 Apply Coupon
**POST** `/cart/apply-coupon`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "coupon_code": "SAVE20"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Coupon applied successfully",
  "data": {
    "coupon": {
      "code": "SAVE20",
      "type": "percentage",
      "value": 20,
      "discount_amount": 480.00
    },
    "cart_summary": {
      "subtotal": 2400.00,
      "discount": 480.00,
      "tax": 192.00,
      "shipping": 100.00,
      "total": 2212.00
    }
  }
}
```

---

# 6. ORDER ENDPOINTS

## 6.1 Create Order
**POST** `/orders`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "shipping_address_id": 1,
  "billing_address_id": 1,
  "payment_method": "razorpay",
  "shipping_method": "standard",
  "notes": "Please handle with care",
  "coupon_code": "SAVE20"
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "order": {
      "id": 1,
      "order_number": "ORD-2024-001",
      "user_id": 1,
      "status": "pending",
      "payment_status": "pending",
      "shipping_status": "pending",
      "subtotal": 2400.00,
      "tax": 192.00,
      "shipping": 100.00,
      "discount": 480.00,
      "total": 2212.00,
      "currency": "INR",
      "items": [
        {
          "id": 1,
          "product_id": 1,
          "product_variant_id": 2,
          "quantity": 2,
          "price": 1200.00,
          "total": 2400.00,
          "product": {
            "id": 1,
            "name": "Ganesha Murti",
            "sku": "GM001-M",
            "image": "https://example.com/image1.jpg"
          }
        }
      ],
      "shipping_address": {
        "id": 1,
        "name": "John Doe",
        "phone": "+************",
        "address_line_1": "123 Main Street",
        "address_line_2": "Apartment 4B",
        "city": "Mumbai",
        "state": "Maharashtra",
        "postal_code": "400001",
        "country": "India"
      },
      "billing_address": {
        "id": 1,
        "name": "John Doe",
        "phone": "+************",
        "address_line_1": "123 Main Street",
        "city": "Mumbai",
        "state": "Maharashtra",
        "postal_code": "400001",
        "country": "India"
      },
      "payment": {
        "method": "razorpay",
        "status": "pending",
        "transaction_id": null,
        "gateway_order_id": "order_razorpay_123"
      },
      "shipping": {
        "method": "standard",
        "estimated_delivery": "2024-01-07",
        "tracking_number": null
      },
      "notes": "Please handle with care",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 6.2 Get User Orders
**GET** `/orders`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
page=1
per_page=10
status=pending|processing|shipped|delivered|cancelled
```

### Response (200)
```json
{
  "success": true,
  "message": "Orders retrieved successfully",
  "data": {
    "orders": [
      {
        "id": 1,
        "order_number": "ORD-2024-001",
        "status": "processing",
        "payment_status": "paid",
        "shipping_status": "preparing",
        "total": 2212.00,
        "currency": "INR",
        "items_count": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "estimated_delivery": "2024-01-07"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 5,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

## 6.3 Get Single Order
**GET** `/orders/{id}`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Order retrieved successfully",
  "data": {
    "order": {
      "id": 1,
      "order_number": "ORD-2024-001",
      "user_id": 1,
      "status": "processing",
      "payment_status": "paid",
      "shipping_status": "preparing",
      "subtotal": 2400.00,
      "tax": 192.00,
      "shipping": 100.00,
      "discount": 480.00,
      "total": 2212.00,
      "currency": "INR",
      "items": [
        {
          "id": 1,
          "product_id": 1,
          "product_variant_id": 2,
          "quantity": 2,
          "price": 1200.00,
          "total": 2400.00,
          "product": {
            "id": 1,
            "name": "Ganesha Murti",
            "sku": "GM001-M",
            "image": "https://example.com/image1.jpg",
            "description": "Beautiful handcrafted Ganesha murti"
          }
        }
      ],
      "shipping_address": {
        "name": "John Doe",
        "phone": "+************",
        "address_line_1": "123 Main Street",
        "city": "Mumbai",
        "state": "Maharashtra",
        "postal_code": "400001",
        "country": "India"
      },
      "payment": {
        "method": "razorpay",
        "status": "paid",
        "transaction_id": "txn_razorpay_456",
        "gateway_order_id": "order_razorpay_123",
        "paid_at": "2024-01-01T01:00:00Z"
      },
      "shipping": {
        "method": "standard",
        "estimated_delivery": "2024-01-07",
        "tracking_number": "TRK123456789",
        "tracking_url": "https://tracking.example.com/TRK123456789"
      },
      "timeline": [
        {
          "status": "pending",
          "message": "Order placed successfully",
          "created_at": "2024-01-01T00:00:00Z"
        },
        {
          "status": "paid",
          "message": "Payment received",
          "created_at": "2024-01-01T01:00:00Z"
        },
        {
          "status": "processing",
          "message": "Order is being prepared",
          "created_at": "2024-01-01T02:00:00Z"
        }
      ],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T02:00:00Z"
    }
  }
}
```

## 6.4 Cancel Order
**POST** `/orders/{id}/cancel`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "reason": "Changed my mind",
  "notes": "Please process refund"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Order cancelled successfully",
  "data": {
    "order": {
      "id": 1,
      "status": "cancelled",
      "cancellation": {
        "reason": "Changed my mind",
        "notes": "Please process refund",
        "cancelled_at": "2024-01-01T03:00:00Z",
        "refund_status": "pending"
      }
    }
  }
}
```

---

# 7. ADDRESS ENDPOINTS

## 7.1 Get User Addresses
**GET** `/addresses`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Addresses retrieved successfully",
  "data": {
    "addresses": [
      {
        "id": 1,
        "name": "John Doe",
        "phone": "+************",
        "address_line_1": "123 Main Street",
        "address_line_2": "Apartment 4B",
        "city": "Mumbai",
        "state": "Maharashtra",
        "postal_code": "400001",
        "country": "India",
        "type": "home|office|other",
        "is_default": true,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

## 7.2 Add Address
**POST** `/addresses`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "name": "John Doe",
  "phone": "+************",
  "address_line_1": "123 Main Street",
  "address_line_2": "Apartment 4B",
  "city": "Mumbai",
  "state": "Maharashtra",
  "postal_code": "400001",
  "country": "India",
  "type": "home",
  "is_default": false
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Address added successfully",
  "data": {
    "address": {
      "id": 2,
      "name": "John Doe",
      "phone": "+************",
      "address_line_1": "123 Main Street",
      "address_line_2": "Apartment 4B",
      "city": "Mumbai",
      "state": "Maharashtra",
      "postal_code": "400001",
      "country": "India",
      "type": "home",
      "is_default": false,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 7.3 Update Address
**PUT** `/addresses/{id}`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "name": "John Doe Updated",
  "phone": "+************",
  "address_line_1": "456 New Street",
  "city": "Delhi",
  "state": "Delhi",
  "postal_code": "110001",
  "is_default": true
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Address updated successfully",
  "data": {
    "address": {
      "id": 1,
      "name": "John Doe Updated",
      "phone": "+************",
      "address_line_1": "456 New Street",
      "city": "Delhi",
      "state": "Delhi",
      "postal_code": "110001",
      "is_default": true,
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 7.4 Delete Address
**DELETE** `/addresses/{id}`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Address deleted successfully",
  "data": null
}
```

---

# 8. PAYMENT ENDPOINTS

## 8.1 Create Razorpay Order
**POST** `/payments/razorpay/create-order`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "order_id": 1,
  "amount": 2212.00,
  "currency": "INR"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Razorpay order created successfully",
  "data": {
    "razorpay_order_id": "order_razorpay_123",
    "amount": 221200,
    "currency": "INR",
    "key": "rzp_test_key",
    "order_id": 1
  }
}
```

## 8.2 Verify Razorpay Payment
**POST** `/payments/razorpay/verify`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "order_id": 1,
  "razorpay_order_id": "order_razorpay_123",
  "razorpay_payment_id": "pay_razorpay_456",
  "razorpay_signature": "signature_hash"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Payment verified successfully",
  "data": {
    "payment_status": "success",
    "transaction_id": "pay_razorpay_456",
    "order": {
      "id": 1,
      "status": "processing",
      "payment_status": "paid"
    }
  }
}
```

---

# 9. WISHLIST/FAVORITES ENDPOINTS

## 9.1 Get Wishlist
**GET** `/wishlist`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
page=1
per_page=20
sort_by=added_date|name|price|rating
sort_order=desc|asc
category_id=1
in_stock=true|false
on_sale=true|false
```

### Response (200)
```json
{
  "success": true,
  "message": "Wishlist retrieved successfully",
  "data": {
    "wishlist": {
      "id": 1,
      "user_id": 1,
      "name": "My Wishlist",
      "description": "My favorite murtis",
      "is_public": false,
      "items": [
        {
          "id": 1,
          "product_id": 1,
          "product_variant_id": null,
          "notes": "For temple decoration",
          "priority": "high",
          "product": {
            "id": 1,
            "name": "Premium Marble Ganesha Murti",
            "slug": "premium-marble-ganesha-murti",
            "sku": "GM001",
            "price": 2500.00,
            "sale_price": 2000.00,
            "discount_percentage": 20,
            "currency": "INR",
            "stock_quantity": 25,
            "in_stock": true,
            "on_sale": true,
            "featured": true,
            "images": [
              {
                "id": 1,
                "url": "https://example.com/ganesha1.jpg",
                "alt": "Premium Marble Ganesha",
                "is_primary": true
              }
            ],
            "category": {
              "id": 1,
              "name": "Ganesha Murtis",
              "slug": "ganesha-murtis"
            },
            "attributes": [
              {
                "name": "Material",
                "value": "Marble"
              },
              {
                "name": "Size",
                "value": "Medium"
              }
            ],
            "rating": {
              "average": 4.8,
              "count": 156
            },
            "reviews_count": 156,
            "is_available": true,
            "estimated_delivery": "3-5 days",
            "tags": ["handcrafted", "premium", "marble"]
          },
          "price_when_added": 2500.00,
          "price_change": -500.00,
          "price_change_percentage": -20,
          "is_price_dropped": true,
          "is_back_in_stock": false,
          "added_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z"
        }
      ],
      "items_count": 1,
      "total_value": 2000.00,
      "total_savings": 500.00,
      "categories": [
        {
          "id": 1,
          "name": "Ganesha Murtis",
          "count": 1
        }
      ],
      "price_alerts_count": 0,
      "stock_alerts_count": 0,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 1,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    },
    "summary": {
      "total_items": 1,
      "in_stock_items": 1,
      "out_of_stock_items": 0,
      "on_sale_items": 1,
      "price_dropped_items": 1,
      "back_in_stock_items": 0
    }
  }
}
```

## 9.2 Add to Wishlist
**POST** `/wishlist/add`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "product_id": 1,
  "product_variant_id": 2,
  "notes": "For temple decoration",
  "priority": "high|medium|low",
  "enable_price_alert": true,
  "enable_stock_alert": true,
  "target_price": 1800.00
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Product added to wishlist successfully",
  "data": {
    "wishlist_item": {
      "id": 1,
      "product_id": 1,
      "product_variant_id": 2,
      "notes": "For temple decoration",
      "priority": "high",
      "price_when_added": 2000.00,
      "target_price": 1800.00,
      "price_alert_enabled": true,
      "stock_alert_enabled": true,
      "product": {
        "id": 1,
        "name": "Premium Marble Ganesha Murti",
        "price": 2000.00,
        "image": "https://example.com/ganesha1.jpg",
        "in_stock": true
      },
      "added_at": "2024-01-01T00:00:00Z"
    },
    "wishlist_summary": {
      "items_count": 1,
      "total_value": 2000.00
    }
  }
}
```

## 9.3 Update Wishlist Item
**PUT** `/wishlist/items/{item_id}`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "notes": "Updated notes for temple",
  "priority": "medium",
  "enable_price_alert": false,
  "enable_stock_alert": true,
  "target_price": 1500.00
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Wishlist item updated successfully",
  "data": {
    "wishlist_item": {
      "id": 1,
      "notes": "Updated notes for temple",
      "priority": "medium",
      "target_price": 1500.00,
      "price_alert_enabled": false,
      "stock_alert_enabled": true,
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 9.4 Remove from Wishlist
**DELETE** `/wishlist/items/{item_id}`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Product removed from wishlist successfully",
  "data": {
    "wishlist_summary": {
      "items_count": 0,
      "total_value": 0.00
    }
  }
}
```

## 9.5 Move to Cart
**POST** `/wishlist/items/{item_id}/move-to-cart`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "quantity": 1,
  "remove_from_wishlist": true
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Product moved to cart successfully",
  "data": {
    "cart_item": {
      "id": 1,
      "product_id": 1,
      "quantity": 1,
      "price": 2000.00
    },
    "wishlist_updated": true,
    "cart_summary": {
      "items_count": 1,
      "total": 2000.00
    }
  }
}
```

## 9.6 Check Product in Wishlist
**GET** `/wishlist/check/{product_id}`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Wishlist status retrieved successfully",
  "data": {
    "is_wishlisted": true,
    "wishlist_item": {
      "id": 1,
      "added_at": "2024-01-01T00:00:00Z",
      "priority": "high"
    }
  }
}
```

## 9.7 Bulk Add to Wishlist
**POST** `/wishlist/bulk-add`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "products": [
    {
      "product_id": 1,
      "product_variant_id": 2,
      "priority": "high"
    },
    {
      "product_id": 2,
      "product_variant_id": null,
      "priority": "medium"
    }
  ]
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Products added to wishlist successfully",
  "data": {
    "added_items": [
      {
        "product_id": 1,
        "wishlist_item_id": 1,
        "status": "added"
      },
      {
        "product_id": 2,
        "wishlist_item_id": 2,
        "status": "added"
      }
    ],
    "failed_items": [],
    "wishlist_summary": {
      "items_count": 2,
      "total_value": 4000.00
    }
  }
}
```

## 9.8 Clear Wishlist
**DELETE** `/wishlist/clear`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Wishlist cleared successfully",
  "data": {
    "cleared_items_count": 5,
    "wishlist_summary": {
      "items_count": 0,
      "total_value": 0.00
    }
  }
}
```

## 9.9 Get Wishlist Analytics
**GET** `/wishlist/analytics`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Wishlist analytics retrieved successfully",
  "data": {
    "analytics": {
      "total_items_ever_added": 25,
      "total_items_purchased": 8,
      "total_items_removed": 12,
      "current_items": 5,
      "conversion_rate": 32.0,
      "average_time_in_wishlist": "15 days",
      "most_wishlisted_category": {
        "id": 1,
        "name": "Ganesha Murtis",
        "count": 15
      },
      "price_range_distribution": [
        {
          "range": "₹0 - ₹1,000",
          "count": 2
        },
        {
          "range": "₹1,000 - ₹5,000",
          "count": 3
        }
      ],
      "monthly_activity": [
        {
          "month": "2024-01",
          "added": 5,
          "removed": 2,
          "purchased": 1
        }
      ]
    }
  }
}
```

## 9.10 Share Wishlist
**POST** `/wishlist/share`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "share_type": "public_link|email|whatsapp",
  "recipients": ["<EMAIL>"],
  "message": "Check out my favorite murtis!",
  "expiry_days": 30
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Wishlist shared successfully",
  "data": {
    "share_link": "https://ghanshyammurtibhandar.com/wishlist/shared/abc123",
    "share_id": "abc123",
    "expires_at": "2024-02-01T00:00:00Z",
    "recipients_notified": 1
  }
}
```

---

# 10. PRODUCT COMPARISON ENDPOINTS

## 10.1 Add to Compare
**POST** `/compare/add`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "product_id": 1
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Product added to comparison successfully",
  "data": {
    "compare_item": {
      "id": 1,
      "product_id": 1,
      "added_at": "2024-01-01T00:00:00Z"
    },
    "compare_summary": {
      "items_count": 1,
      "max_items": 4
    }
  }
}
```

## 10.2 Get Compare List
**GET** `/compare`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Compare list retrieved successfully",
  "data": {
    "compare": {
      "id": 1,
      "user_id": 1,
      "items": [
        {
          "id": 1,
          "product_id": 1,
          "product": {
            "id": 1,
            "name": "Premium Marble Ganesha Murti",
            "slug": "premium-marble-ganesha-murti",
            "price": 2500.00,
            "sale_price": 2000.00,
            "images": [
              {
                "url": "https://example.com/ganesha1.jpg",
                "alt": "Premium Marble Ganesha"
              }
            ],
            "rating": {
              "average": 4.8,
              "count": 156
            },
            "attributes": [
              {
                "name": "Material",
                "value": "Marble"
              },
              {
                "name": "Size",
                "value": "Medium (20cm)"
              },
              {
                "name": "Weight",
                "value": "3.5 kg"
              },
              {
                "name": "Color",
                "value": "White"
              },
              {
                "name": "Finish",
                "value": "Polished"
              }
            ],
            "specifications": [
              {
                "name": "Dimensions",
                "value": "20cm x 15cm x 25cm"
              },
              {
                "name": "Origin",
                "value": "Rajasthan"
              },
              {
                "name": "Craftsmanship",
                "value": "Handcrafted"
              }
            ],
            "features": [
              "Premium quality marble",
              "Handcrafted by skilled artisans",
              "Polished finish",
              "Suitable for home and temple"
            ],
            "in_stock": true,
            "stock_quantity": 25
          },
          "added_at": "2024-01-01T00:00:00Z"
        },
        {
          "id": 2,
          "product_id": 2,
          "product": {
            "id": 2,
            "name": "Traditional Brass Ganesha Murti",
            "slug": "traditional-brass-ganesha-murti",
            "price": 1800.00,
            "sale_price": 1500.00,
            "images": [
              {
                "url": "https://example.com/brass-ganesha1.jpg",
                "alt": "Traditional Brass Ganesha"
              }
            ],
            "rating": {
              "average": 4.6,
              "count": 89
            },
            "attributes": [
              {
                "name": "Material",
                "value": "Brass"
              },
              {
                "name": "Size",
                "value": "Medium (18cm)"
              },
              {
                "name": "Weight",
                "value": "2.8 kg"
              },
              {
                "name": "Color",
                "value": "Golden"
              },
              {
                "name": "Finish",
                "value": "Antique"
              }
            ],
            "specifications": [
              {
                "name": "Dimensions",
                "value": "18cm x 12cm x 22cm"
              },
              {
                "name": "Origin",
                "value": "Tamil Nadu"
              },
              {
                "name": "Craftsmanship",
                "value": "Traditional casting"
              }
            ],
            "features": [
              "Pure brass construction",
              "Traditional design",
              "Antique finish",
              "Durable and long-lasting"
            ],
            "in_stock": true,
            "stock_quantity": 18
          },
          "added_at": "2024-01-01T01:00:00Z"
        }
      ],
      "items_count": 2,
      "max_items": 4,
      "comparison_attributes": [
        {
          "name": "Material",
          "values": ["Marble", "Brass"],
          "type": "categorical"
        },
        {
          "name": "Price",
          "values": [2000.00, 1500.00],
          "type": "numeric",
          "unit": "INR"
        },
        {
          "name": "Weight",
          "values": [3.5, 2.8],
          "type": "numeric",
          "unit": "kg"
        },
        {
          "name": "Rating",
          "values": [4.8, 4.6],
          "type": "numeric",
          "unit": "stars"
        }
      ],
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 10.3 Remove from Compare
**DELETE** `/compare/items/{item_id}`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Product removed from comparison successfully",
  "data": {
    "compare_summary": {
      "items_count": 1,
      "max_items": 4
    }
  }
}
```

## 10.4 Clear Compare List
**DELETE** `/compare/clear`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Compare list cleared successfully",
  "data": {
    "cleared_items_count": 2
  }
}
```

---

# 11. FILTER ENDPOINTS

## 11.1 Get Available Filters
**GET** `/filters`

### Query Parameters
```
category_id=1
subcategory_id=2
search_query=ganesha
```

### Response (200)
```json
{
  "success": true,
  "message": "Filters retrieved successfully",
  "data": {
    "filters": {
      "price": {
        "type": "range",
        "min": 500.00,
        "max": 25000.00,
        "step": 100.00,
        "currency": "INR",
        "popular_ranges": [
          {
            "min": 500,
            "max": 1500,
            "label": "₹500 - ₹1,500",
            "count": 25
          },
          {
            "min": 1500,
            "max": 5000,
            "label": "₹1,500 - ₹5,000",
            "count": 45
          }
        ]
      },
      "categories": {
        "type": "multi_select",
        "options": [
          {
            "id": 1,
            "name": "Ganesha Murtis",
            "slug": "ganesha-murtis",
            "count": 45,
            "image": "https://example.com/cat1.jpg"
          },
          {
            "id": 2,
            "name": "Lakshmi Murtis",
            "slug": "lakshmi-murtis",
            "count": 32,
            "image": "https://example.com/cat2.jpg"
          }
        ]
      },
      "materials": {
        "type": "multi_select",
        "options": [
          {
            "value": "marble",
            "label": "Marble",
            "count": 35,
            "image": "https://example.com/material-marble.jpg"
          },
          {
            "value": "brass",
            "label": "Brass",
            "count": 28,
            "image": "https://example.com/material-brass.jpg"
          },
          {
            "value": "stone",
            "label": "Stone",
            "count": 20,
            "image": "https://example.com/material-stone.jpg"
          }
        ]
      },
      "colors": {
        "type": "multi_select",
        "display": "color_palette",
        "options": [
          {
            "value": "white",
            "label": "White",
            "hex": "#FFFFFF",
            "count": 40
          },
          {
            "value": "golden",
            "label": "Golden",
            "hex": "#FFD700",
            "count": 25
          },
          {
            "value": "black",
            "label": "Black",
            "hex": "#000000",
            "count": 15
          }
        ]
      },
      "sizes": {
        "type": "multi_select",
        "options": [
          {
            "value": "small",
            "label": "Small",
            "description": "Up to 15cm",
            "count": 20
          },
          {
            "value": "medium",
            "label": "Medium",
            "description": "15-25cm",
            "count": 35
          },
          {
            "value": "large",
            "label": "Large",
            "description": "25-40cm",
            "count": 18
          },
          {
            "value": "extra_large",
            "label": "Extra Large",
            "description": "Above 40cm",
            "count": 5
          }
        ]
      },
      "weight": {
        "type": "range",
        "min": 0.5,
        "max": 15.0,
        "step": 0.5,
        "unit": "kg"
      },
      "rating": {
        "type": "single_select",
        "options": [
          {
            "value": 5,
            "label": "5 Stars",
            "count": 25
          },
          {
            "value": 4,
            "label": "4 Stars & Above",
            "count": 60
          },
          {
            "value": 3,
            "label": "3 Stars & Above",
            "count": 75
          }
        ]
      },
      "availability": {
        "type": "multi_select",
        "options": [
          {
            "value": "in_stock",
            "label": "In Stock",
            "count": 68
          },
          {
            "value": "featured",
            "label": "Featured",
            "count": 25
          },
          {
            "value": "on_sale",
            "label": "On Sale",
            "count": 30
          },
          {
            "value": "new_arrivals",
            "label": "New Arrivals",
            "count": 12
          }
        ]
      },
      "origin": {
        "type": "multi_select",
        "options": [
          {
            "value": "rajasthan",
            "label": "Rajasthan",
            "count": 35
          },
          {
            "value": "tamil_nadu",
            "label": "Tamil Nadu",
            "count": 25
          },
          {
            "value": "uttar_pradesh",
            "label": "Uttar Pradesh",
            "count": 18
          }
        ]
      }
    },
    "meta": {
      "total_products": 78,
      "filter_combinations": 1250
    }
  }
}
```

---

# 12. REVIEW ENDPOINTS

## 10.1 Get Product Reviews
**GET** `/products/{product_id}/reviews`

### Query Parameters
```
page=1
per_page=10
sort_by=rating|date
sort_order=desc|asc
rating=5
```

### Response (200)
```json
{
  "success": true,
  "message": "Reviews retrieved successfully",
  "data": {
    "reviews": [
      {
        "id": 1,
        "user_id": 1,
        "user_name": "Priya Sharma",
        "user_avatar": "https://example.com/avatar.jpg",
        "rating": 5,
        "title": "Excellent Quality",
        "comment": "Beautiful murti, excellent quality and fast delivery!",
        "images": [
          "https://example.com/review1.jpg"
        ],
        "helpful_count": 5,
        "verified_purchase": true,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "rating_summary": {
      "average": 4.5,
      "count": 25,
      "breakdown": {
        "5": 15,
        "4": 8,
        "3": 2,
        "2": 0,
        "1": 0
      }
    },
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 25,
      "total_pages": 3
    }
  }
}
```

## 10.2 Add Product Review
**POST** `/products/{product_id}/reviews`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "rating": 5,
  "title": "Excellent Quality",
  "comment": "Beautiful murti, excellent quality and fast delivery!",
  "images": ["base64_image_data"]
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Review added successfully",
  "data": {
    "review": {
      "id": 1,
      "user_id": 1,
      "product_id": 1,
      "rating": 5,
      "title": "Excellent Quality",
      "comment": "Beautiful murti, excellent quality and fast delivery!",
      "images": ["https://example.com/review1.jpg"],
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

---

# 11. NOTIFICATION ENDPOINTS

## 11.1 Get Notifications
**GET** `/notifications`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
page=1
per_page=20
type=order|promotion|general
read=true|false
```

### Response (200)
```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": 1,
        "type": "order",
        "title": "Order Shipped",
        "message": "Your order ORD-2024-001 has been shipped",
        "data": {
          "order_id": 1,
          "tracking_number": "TRK123456789"
        },
        "read_at": null,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "unread_count": 5,
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 10,
      "total_pages": 1
    }
  }
}
```

## 11.2 Mark Notification as Read
**POST** `/notifications/{id}/read`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Notification marked as read",
  "data": {
    "notification": {
      "id": 1,
      "read_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 11.3 Mark All Notifications as Read
**POST** `/notifications/read-all`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "All notifications marked as read",
  "data": {
    "updated_count": 5
  }
}
```

---

# 12. COUPON ENDPOINTS

## 12.1 Validate Coupon
**POST** `/coupons/validate`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "coupon_code": "SAVE20",
  "cart_total": 2400.00
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Coupon is valid",
  "data": {
    "coupon": {
      "code": "SAVE20",
      "type": "percentage",
      "value": 20,
      "minimum_amount": 1000.00,
      "maximum_discount": 500.00,
      "discount_amount": 480.00,
      "valid_until": "2024-12-31T23:59:59Z"
    }
  }
}
```

## 12.2 Get Available Coupons
**GET** `/coupons/available`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Available coupons retrieved successfully",
  "data": {
    "coupons": [
      {
        "code": "SAVE20",
        "title": "20% Off",
        "description": "Get 20% off on orders above ₹1000",
        "type": "percentage",
        "value": 20,
        "minimum_amount": 1000.00,
        "maximum_discount": 500.00,
        "valid_until": "2024-12-31T23:59:59Z"
      }
    ]
  }
}
```

---

# 13. RECOMMENDATION ENDPOINTS

## 13.1 Get Personalized Recommendations
**GET** `/recommendations/personalized`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
type=homepage|category|product|cart|checkout
limit=20
category_id=1
product_id=1
```

### Response (200)
```json
{
  "success": true,
  "message": "Personalized recommendations retrieved successfully",
  "data": {
    "recommendations": [
      {
        "section": "recommended_for_you",
        "title": "Recommended for You",
        "description": "Based on your browsing and purchase history",
        "algorithm": "collaborative_filtering",
        "products": [
          {
            "id": 1,
            "name": "Premium Marble Ganesha Murti",
            "slug": "premium-marble-ganesha-murti",
            "price": 2500.00,
            "sale_price": 2000.00,
            "image": "https://example.com/ganesha1.jpg",
            "rating": 4.8,
            "recommendation_score": 0.95,
            "recommendation_reason": "Similar to items you've viewed",
            "in_stock": true
          }
        ]
      },
      {
        "section": "trending_now",
        "title": "Trending Now",
        "description": "Popular items among customers like you",
        "algorithm": "trending",
        "products": [
          {
            "id": 2,
            "name": "Traditional Brass Lakshmi",
            "price": 1800.00,
            "image": "https://example.com/lakshmi1.jpg",
            "rating": 4.6,
            "recommendation_score": 0.88,
            "recommendation_reason": "Trending in your area"
          }
        ]
      },
      {
        "section": "frequently_bought_together",
        "title": "Frequently Bought Together",
        "description": "Customers who bought this also bought",
        "algorithm": "market_basket",
        "products": [
          {
            "id": 3,
            "name": "Brass Diya Set",
            "price": 500.00,
            "image": "https://example.com/diya1.jpg",
            "rating": 4.4,
            "recommendation_score": 0.82,
            "recommendation_reason": "Often bought with Ganesha murtis"
          }
        ]
      }
    ],
    "user_profile": {
      "preferences": {
        "favorite_categories": ["Ganesha Murtis", "Lakshmi Murtis"],
        "preferred_materials": ["Marble", "Brass"],
        "price_range": {
          "min": 1000,
          "max": 5000
        }
      },
      "behavior": {
        "last_viewed_category": "Ganesha Murtis",
        "average_order_value": 3500.00,
        "purchase_frequency": "monthly"
      }
    },
    "meta": {
      "recommendation_id": "rec_uuid_123",
      "generated_at": "2024-01-01T00:00:00Z",
      "expires_at": "2024-01-01T01:00:00Z"
    }
  }
}
```

## 13.2 Get Similar Products
**GET** `/products/{product_id}/similar`

### Query Parameters
```
limit=10
algorithm=content_based|collaborative|hybrid
include_variants=true|false
```

### Response (200)
```json
{
  "success": true,
  "message": "Similar products retrieved successfully",
  "data": {
    "similar_products": [
      {
        "id": 2,
        "name": "Elegant Marble Ganesha",
        "slug": "elegant-marble-ganesha",
        "price": 2200.00,
        "sale_price": 1800.00,
        "image": "https://example.com/ganesha2.jpg",
        "rating": 4.7,
        "similarity_score": 0.92,
        "similarity_reasons": [
          "Same material (Marble)",
          "Similar size (Medium)",
          "Same category (Ganesha Murtis)"
        ],
        "in_stock": true
      }
    ],
    "algorithm_used": "hybrid",
    "similarity_factors": [
      {
        "factor": "material",
        "weight": 0.4
      },
      {
        "factor": "category",
        "weight": 0.3
      },
      {
        "factor": "price_range",
        "weight": 0.2
      },
      {
        "factor": "customer_behavior",
        "weight": 0.1
      }
    ]
  }
}
```

## 13.3 Get Recently Viewed Products
**GET** `/products/recently-viewed`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
limit=20
```

### Response (200)
```json
{
  "success": true,
  "message": "Recently viewed products retrieved successfully",
  "data": {
    "recently_viewed": [
      {
        "id": 1,
        "name": "Premium Marble Ganesha Murti",
        "slug": "premium-marble-ganesha-murti",
        "price": 2000.00,
        "image": "https://example.com/ganesha1.jpg",
        "rating": 4.8,
        "viewed_at": "2024-01-01T10:30:00Z",
        "view_count": 5,
        "time_spent": "2 minutes 30 seconds",
        "in_stock": true
      }
    ],
    "total_viewed": 15,
    "viewing_patterns": {
      "most_viewed_category": "Ganesha Murtis",
      "average_time_per_product": "1 minute 45 seconds",
      "peak_viewing_time": "evening"
    }
  }
}
```

## 13.4 Track Product View
**POST** `/products/{product_id}/view`
**Headers:** Authorization: Bearer <token> (optional)

### Request Body
```json
{
  "source": "search|category|recommendation|direct",
  "referrer": "homepage|search_results|category_page",
  "session_id": "session_uuid_123",
  "device_info": {
    "type": "mobile|desktop|tablet",
    "os": "android|ios|web"
  }
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Product view tracked successfully",
  "data": {
    "view_id": "view_uuid_456",
    "recommendations_updated": true,
    "similar_products": [
      {
        "id": 2,
        "name": "Similar Product",
        "price": 1800.00,
        "image": "https://example.com/similar1.jpg"
      }
    ]
  }
}
```

## 13.5 Get Trending Products
**GET** `/products/trending`

### Query Parameters
```
period=today|week|month|all_time
category_id=1
limit=20
region=mumbai|delhi|bangalore
```

### Response (200)
```json
{
  "success": true,
  "message": "Trending products retrieved successfully",
  "data": {
    "trending_products": [
      {
        "id": 1,
        "name": "Premium Marble Ganesha Murti",
        "slug": "premium-marble-ganesha-murti",
        "price": 2000.00,
        "image": "https://example.com/ganesha1.jpg",
        "rating": 4.8,
        "trend_score": 95,
        "trend_change": "+15%",
        "views_count": 1250,
        "orders_count": 45,
        "trending_since": "2024-01-01T00:00:00Z",
        "in_stock": true
      }
    ],
    "trending_categories": [
      {
        "id": 1,
        "name": "Ganesha Murtis",
        "trend_score": 88,
        "trend_change": "+12%"
      }
    ],
    "period": "week",
    "region": "mumbai",
    "last_updated": "2024-01-01T00:00:00Z"
  }
}
```

---

# 14. ANALYTICS & TRACKING ENDPOINTS

## 14.1 Track User Event
**POST** `/analytics/track`
**Headers:** Authorization: Bearer <token> (optional)

### Request Body
```json
{
  "event_type": "product_view|add_to_cart|purchase|search|filter_applied",
  "event_data": {
    "product_id": 1,
    "category_id": 1,
    "search_query": "ganesha murti",
    "filters_applied": ["material:marble", "price:1000-5000"],
    "source": "search_results",
    "value": 2000.00
  },
  "session_id": "session_uuid_123",
  "device_info": {
    "type": "mobile",
    "os": "android",
    "app_version": "1.0.0"
  },
  "location": {
    "city": "Mumbai",
    "state": "Maharashtra",
    "country": "India"
  },
  "timestamp": "2024-01-01T10:30:00Z"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Event tracked successfully",
  "data": {
    "event_id": "event_uuid_789",
    "processed": true,
    "recommendations_updated": true
  }
}
```

## 14.2 Get User Analytics
**GET** `/analytics/user`
**Headers:** Authorization: Bearer <token>

### Query Parameters
```
period=week|month|quarter|year
```

### Response (200)
```json
{
  "success": true,
  "message": "User analytics retrieved successfully",
  "data": {
    "analytics": {
      "browsing_behavior": {
        "total_sessions": 25,
        "total_page_views": 150,
        "average_session_duration": "8 minutes 30 seconds",
        "bounce_rate": 25.5,
        "most_viewed_categories": [
          {
            "category": "Ganesha Murtis",
            "views": 45,
            "percentage": 30
          }
        ]
      },
      "purchase_behavior": {
        "total_orders": 3,
        "total_spent": 8500.00,
        "average_order_value": 2833.33,
        "favorite_categories": ["Ganesha Murtis", "Lakshmi Murtis"],
        "preferred_price_range": {
          "min": 1500,
          "max": 4000
        }
      },
      "engagement": {
        "wishlist_items": 5,
        "reviews_written": 2,
        "products_shared": 3,
        "referrals_made": 1
      },
      "recommendations": {
        "accuracy_score": 85.5,
        "click_through_rate": 12.3,
        "conversion_rate": 8.7
      }
    },
    "period": "month",
    "generated_at": "2024-01-01T00:00:00Z"
  }
}
```

---

# 15. SUPPORT ENDPOINTS

## 13.1 Submit Support Ticket
**POST** `/support/tickets`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "subject": "Order Issue",
  "category": "order|product|payment|general",
  "priority": "low|medium|high",
  "message": "I have an issue with my recent order",
  "order_id": 1,
  "attachments": ["base64_image_data"]
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Support ticket created successfully",
  "data": {
    "ticket": {
      "id": 1,
      "ticket_number": "TKT-2024-001",
      "subject": "Order Issue",
      "category": "order",
      "priority": "medium",
      "status": "open",
      "message": "I have an issue with my recent order",
      "attachments": ["https://example.com/attachment1.jpg"],
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 13.2 Get Support Tickets
**GET** `/support/tickets`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Support tickets retrieved successfully",
  "data": {
    "tickets": [
      {
        "id": 1,
        "ticket_number": "TKT-2024-001",
        "subject": "Order Issue",
        "status": "open",
        "priority": "medium",
        "last_reply_at": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

---

# 14. MISCELLANEOUS ENDPOINTS

## 14.1 Get App Configuration
**GET** `/config`

### Response (200)
```json
{
  "success": true,
  "message": "Configuration retrieved successfully",
  "data": {
    "config": {
      "app_version": "1.0.0",
      "min_supported_version": "1.0.0",
      "force_update": false,
      "maintenance_mode": false,
      "razorpay_key": "rzp_test_key",
      "currency": "INR",
      "shipping_charges": 100.00,
      "free_shipping_threshold": 2000.00,
      "tax_rate": 10.0,
      "contact": {
        "phone": "+************",
        "email": "<EMAIL>",
        "address": "123 Temple Street, Mumbai, Maharashtra 400001"
      },
      "social_links": {
        "facebook": "https://facebook.com/ghanshyammurtibhandar",
        "instagram": "https://instagram.com/ghanshyammurtibhandar",
        "whatsapp": "https://wa.me/************"
      }
    }
  }
}
```

## 14.2 Search Suggestions
**GET** `/search/suggestions`

### Query Parameters
```
q=ganesha
limit=10
```

### Response (200)
```json
{
  "success": true,
  "message": "Search suggestions retrieved successfully",
  "data": {
    "suggestions": [
      "ganesha murti",
      "ganesha marble murti",
      "ganesha brass murti",
      "small ganesha murti"
    ]
  }
}
```

---

# 15. RATE LIMITING

All API endpoints are rate limited:
- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour
- **Login attempts**: 5 attempts per 15 minutes per IP

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

---

# 16. WEBHOOKS

## 16.1 Payment Webhook (Razorpay)
**POST** `/webhooks/razorpay`

### Request Body
```json
{
  "entity": "event",
  "account_id": "acc_razorpay",
  "event": "payment.captured",
  "contains": ["payment"],
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_razorpay_456",
        "amount": 221200,
        "currency": "INR",
        "status": "captured",
        "order_id": "order_razorpay_123"
      }
    }
  },
  "created_at": **********
}
```

---

# 16. INVENTORY & STOCK ENDPOINTS

## 16.1 Check Product Availability
**GET** `/products/{product_id}/availability`

### Query Parameters
```
variant_id=1
quantity=2
pincode=400001
```

### Response (200)
```json
{
  "success": true,
  "message": "Product availability checked successfully",
  "data": {
    "availability": {
      "product_id": 1,
      "variant_id": 1,
      "in_stock": true,
      "stock_quantity": 25,
      "requested_quantity": 2,
      "available_quantity": 25,
      "stock_status": "in_stock|low_stock|out_of_stock|pre_order",
      "low_stock_threshold": 5,
      "estimated_restock_date": null,
      "delivery_info": {
        "pincode": "400001",
        "serviceable": true,
        "estimated_delivery": "3-5 days",
        "delivery_charges": 100.00,
        "free_delivery_threshold": 2000.00,
        "express_delivery_available": true,
        "express_delivery_charges": 200.00,
        "express_delivery_time": "1-2 days"
      },
      "warehouse_info": {
        "warehouse_id": "WH001",
        "location": "Mumbai",
        "distance": "15 km"
      },
      "stock_alerts": {
        "low_stock_alert": false,
        "out_of_stock_alert": false,
        "restock_alert_enabled": false
      }
    }
  }
}
```

## 16.2 Get Stock Alerts
**GET** `/stock-alerts`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Stock alerts retrieved successfully",
  "data": {
    "alerts": [
      {
        "id": 1,
        "product_id": 1,
        "product_name": "Premium Marble Ganesha Murti",
        "alert_type": "back_in_stock|low_stock|price_drop",
        "current_status": "out_of_stock",
        "target_quantity": 1,
        "target_price": 1800.00,
        "notification_methods": ["email", "push", "sms"],
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "triggered_at": null
      }
    ],
    "active_alerts_count": 3,
    "triggered_alerts_count": 1
  }
}
```

## 16.3 Create Stock Alert
**POST** `/stock-alerts`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "product_id": 1,
  "variant_id": 2,
  "alert_type": "back_in_stock|low_stock|price_drop",
  "target_quantity": 1,
  "target_price": 1800.00,
  "notification_methods": ["email", "push"],
  "auto_add_to_cart": false
}
```

### Response (201)
```json
{
  "success": true,
  "message": "Stock alert created successfully",
  "data": {
    "alert": {
      "id": 1,
      "product_id": 1,
      "alert_type": "back_in_stock",
      "target_quantity": 1,
      "notification_methods": ["email", "push"],
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

---

# 17. SHIPPING & DELIVERY ENDPOINTS

## 17.1 Get Shipping Methods
**GET** `/shipping/methods`

### Query Parameters
```
pincode=400001
cart_total=2500.00
weight=3.5
```

### Response (200)
```json
{
  "success": true,
  "message": "Shipping methods retrieved successfully",
  "data": {
    "shipping_methods": [
      {
        "id": 1,
        "name": "Standard Delivery",
        "description": "Regular delivery within 5-7 business days",
        "cost": 100.00,
        "free_threshold": 2000.00,
        "estimated_days": "5-7",
        "is_free": false,
        "is_express": false,
        "tracking_available": true,
        "insurance_included": false,
        "restrictions": {
          "max_weight": 10.0,
          "max_dimensions": "50x50x50 cm"
        }
      },
      {
        "id": 2,
        "name": "Express Delivery",
        "description": "Fast delivery within 1-2 business days",
        "cost": 200.00,
        "free_threshold": 5000.00,
        "estimated_days": "1-2",
        "is_free": false,
        "is_express": true,
        "tracking_available": true,
        "insurance_included": true,
        "restrictions": {
          "max_weight": 5.0,
          "max_dimensions": "30x30x30 cm"
        }
      },
      {
        "id": 3,
        "name": "Free Delivery",
        "description": "Free delivery for orders above ₹2000",
        "cost": 0.00,
        "free_threshold": 2000.00,
        "estimated_days": "5-7",
        "is_free": true,
        "is_express": false,
        "tracking_available": true,
        "insurance_included": false,
        "available": true
      }
    ],
    "delivery_info": {
      "pincode": "400001",
      "city": "Mumbai",
      "state": "Maharashtra",
      "serviceable": true,
      "cod_available": true,
      "same_day_delivery": false
    }
  }
}
```

---

This comprehensive API documentation covers ALL essential endpoints for your Ghanshyam Murti Bhandar ecommerce application, including:

## 🎯 **COMPLETE FEATURE COVERAGE:**

### **Core Ecommerce Features:**
✅ **Authentication & User Management** - Registration, login, profile, addresses
✅ **Product Catalog** - Products, categories, variants, attributes, images
✅ **Advanced Search** - Autocomplete, filters, saved searches, history
✅ **Shopping Cart** - Add, update, remove, coupons, bulk operations
✅ **Wishlist/Favorites** - Advanced wishlist with alerts, sharing, analytics
✅ **Order Management** - Create, track, cancel, history, timeline
✅ **Payment Integration** - Razorpay complete integration with webhooks

### **Advanced Features:**
✅ **Product Comparison** - Side-by-side comparison with detailed attributes
✅ **Recommendations** - Personalized, trending, similar products, AI-powered
✅ **Reviews & Ratings** - Complete review system with images and verification
✅ **Inventory Management** - Stock checking, alerts, availability tracking
✅ **Shipping & Delivery** - Multiple methods, cost calculation, tracking
✅ **Analytics & Tracking** - User behavior, conversion tracking, insights

### **Business Intelligence:**
✅ **Filter System** - Dynamic filters with real-time counts
✅ **Category Management** - Hierarchical categories with SEO optimization
✅ **Notification System** - Push notifications, email alerts, SMS
✅ **Support System** - Ticket management, help desk integration
✅ **Coupon System** - Advanced coupon validation and management

### **Technical Excellence:**
✅ **Rate Limiting** - API protection and fair usage
✅ **Webhook Support** - Real-time payment and order updates
✅ **Caching Strategy** - Performance optimization
✅ **Error Handling** - Comprehensive error responses
✅ **Security** - JWT authentication, input validation
✅ **Documentation** - Complete request/response examples

This API specification provides everything needed to build a world-class ecommerce mobile application for your religious murti business! 🕉️```
