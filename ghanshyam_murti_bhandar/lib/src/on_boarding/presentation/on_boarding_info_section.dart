import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/ecommerce_logo.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:lottie/lottie.dart';

class OnBoardingInfoSection extends StatelessWidget {
  const OnBoardingInfoSection.first({super.key}) : first = true;
  const OnBoardingInfoSection.second({super.key}) : first = false;

  final bool first;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;
    final isVerySmallScreen = screenHeight < 600;

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal:
            isVerySmallScreen
                ? 16
                : isSmallScreen
                ? 20
                : 24,
        vertical:
            isVerySmallScreen
                ? 4
                : isSmallScreen
                ? 8
                : 12,
      ),
      child: Column(
        mainAxisAlignment:
            first
                ? MainAxisAlignment.spaceEvenly
                : MainAxisAlignment.spaceBetween,
        children: [
          // Logo (only on first page)
          if (first) ...[
            SizedBox(
              height:
                  isVerySmallScreen
                      ? 40
                      : isSmallScreen
                      ? 50
                      : 60,
              child: Center(
                child: EcommerceLogo(
                  style: TextStyles.appLogo.copyWith(
                    color: Colours.lightThemePrimaryColour,
                    fontSize:
                        isVerySmallScreen
                            ? 18
                            : isSmallScreen
                            ? 22
                            : 26,
                  ),
                ),
              ),
            ),
            SizedBox(
              height:
                  isVerySmallScreen
                      ? 8
                      : isSmallScreen
                      ? 12
                      : 16,
            ),
          ],

          // Lottie Animation
          SizedBox(
            height:
                first
                    ? (isVerySmallScreen
                        ? 200
                        : isSmallScreen
                        ? 250
                        : 300)
                    : (isVerySmallScreen
                        ? 160
                        : isSmallScreen
                        ? 200
                        : 240),
            child: Center(
              child: Lottie.asset(
                first ? Media.onBoardingFemale : Media.onBoardingMale,
                fit: BoxFit.contain,
              ),
            ),
          ),

          SizedBox(
            height:
                first
                    ? (isVerySmallScreen
                        ? 12
                        : isSmallScreen
                        ? 16
                        : 20)
                    : (isVerySmallScreen
                        ? 8
                        : isSmallScreen
                        ? 12
                        : 16),
          ),

          // Content Section
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Title
              Text(
                first
                    ? 'Ghanshyam Murti Bhandar'
                    : 'Welcome to Ghanshyam Murti Bhandar',
                style: TextStyles.headingMedium1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                  fontSize:
                      isVerySmallScreen
                          ? 18
                          : isSmallScreen
                          ? 20
                          : 24,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(
                height:
                    isVerySmallScreen
                        ? 4
                        : isSmallScreen
                        ? 6
                        : 8,
              ),

              // Description
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  first
                      ? 'Your trusted destination for authentic and beautifully crafted religious murtis. Bringing divine blessings to your home since 2015.'
                      : 'Browse through our extensive collection of handcrafted murtis. Each piece is made with devotion and attention to detail by skilled artisans.',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                    height: 1.2,
                    fontSize:
                        isVerySmallScreen
                            ? 11
                            : isSmallScreen
                            ? 12
                            : 13,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: first ? 3 : 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          // Features list
          if (!first) ...[
            SizedBox(
              height:
                  isVerySmallScreen
                      ? 8
                      : isSmallScreen
                      ? 8
                      : 10,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildFeatureItem(
                  context,
                  icon: Icons.verified,
                  title: 'Authentic Quality',
                  description: 'Handcrafted by skilled artisans',
                  isSmallScreen: isSmallScreen,
                  isVerySmallScreen: isVerySmallScreen,
                ),
                _buildFeatureItem(
                  context,
                  icon: Icons.local_shipping,
                  title: 'Safe Delivery',
                  description: 'Secure packaging & fast shipping',
                  isSmallScreen: isSmallScreen,
                  isVerySmallScreen: isVerySmallScreen,
                ),
                _buildFeatureItem(
                  context,
                  icon: Icons.support_agent,
                  title: '24/7 Support',
                  description: 'Always here to help you',
                  isSmallScreen: isSmallScreen,
                  isVerySmallScreen: isVerySmallScreen,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    bool isSmallScreen = false,
    bool isVerySmallScreen = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(
              isVerySmallScreen
                  ? 4
                  : isSmallScreen
                  ? 6
                  : 8,
            ),
            decoration: BoxDecoration(
              color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(
                isVerySmallScreen
                    ? 4
                    : isSmallScreen
                    ? 6
                    : 8,
              ),
            ),
            child: Icon(
              icon,
              color: Colours.lightThemePrimaryColour,
              size:
                  isVerySmallScreen
                      ? 14
                      : isSmallScreen
                      ? 16
                      : 18,
            ),
          ),
          SizedBox(
            width:
                isVerySmallScreen
                    ? 6
                    : isSmallScreen
                    ? 8
                    : 10,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyles.headingSemiBold1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontSize:
                        isVerySmallScreen
                            ? 11
                            : isSmallScreen
                            ? 12
                            : 13,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 1),
                Text(
                  description,
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                    fontSize:
                        isVerySmallScreen
                            ? 9
                            : isSmallScreen
                            ? 10
                            : 11,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
