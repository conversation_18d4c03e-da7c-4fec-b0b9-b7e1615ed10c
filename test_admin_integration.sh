#!/bin/bash

# Test script to verify admin panel integration with backend
BASE_URL="http://localhost:8080/api"

echo "🚀 Testing Admin Panel Integration"
echo "=================================="

# Test 1: Create Admin User
echo "📋 Test 1: Creating Admin User"
ADMIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Admin User",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin"
  }')

echo "Admin creation response: $ADMIN_RESPONSE"

# Extract admin token
ADMIN_TOKEN=$(echo $ADMIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Admin Token: ${ADMIN_TOKEN:0:30}..."
echo ""

# Test 2: Create Sample Categories
echo "📋 Test 2: Creating Sample Categories"

# Category 1: Electronics
CATEGORY1_RESPONSE=$(curl -s -X POST "$BASE_URL/categories" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Electronics",
    "description": "Electronic devices and gadgets"
  }')

echo "Electronics category: $CATEGORY1_RESPONSE"
ELECTRONICS_ID=$(echo $CATEGORY1_RESPONSE | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)

# Category 2: Clothing
CATEGORY2_RESPONSE=$(curl -s -X POST "$BASE_URL/categories" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Clothing",
    "description": "Fashion and apparel"
  }')

echo "Clothing category: $CATEGORY2_RESPONSE"
CLOTHING_ID=$(echo $CATEGORY2_RESPONSE | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)

# Category 3: Home & Garden
CATEGORY3_RESPONSE=$(curl -s -X POST "$BASE_URL/categories" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Home & Garden",
    "description": "Home improvement and gardening supplies"
  }')

echo "Home & Garden category: $CATEGORY3_RESPONSE"
HOME_ID=$(echo $CATEGORY3_RESPONSE | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)
echo ""

# Test 3: Create Sample Products
echo "📋 Test 3: Creating Sample Products"

# Product 1: Smartphone
PRODUCT1_RESPONSE=$(curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Smartphone Pro Max\",
    \"description\": \"Latest flagship smartphone with advanced features\",
    \"price\": 79999,
    \"originalPrice\": 89999,
    \"category\": \"$ELECTRONICS_ID\",
    \"stock\": 50,
    \"isActive\": true
  }")

echo "Smartphone product: $PRODUCT1_RESPONSE"

# Product 2: T-Shirt
PRODUCT2_RESPONSE=$(curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Cotton T-Shirt\",
    \"description\": \"Comfortable cotton t-shirt for everyday wear\",
    \"price\": 999,
    \"originalPrice\": 1299,
    \"category\": \"$CLOTHING_ID\",
    \"stock\": 100,
    \"isActive\": true
  }")

echo "T-Shirt product: $PRODUCT2_RESPONSE"

# Product 3: Garden Tool
PRODUCT3_RESPONSE=$(curl -s -X POST "$BASE_URL/products" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Garden Shovel\",
    \"description\": \"Durable steel garden shovel for all your gardening needs\",
    \"price\": 1499,
    \"originalPrice\": 1999,
    \"category\": \"$HOME_ID\",
    \"stock\": 25,
    \"isActive\": true
  }")

echo "Garden Shovel product: $PRODUCT3_RESPONSE"
echo ""

# Test 4: Create Regular User
echo "📋 Test 4: Creating Regular User"
USER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "user123",
    "role": "user"
  }')

echo "User creation response: $USER_RESPONSE"
USER_TOKEN=$(echo $USER_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo ""

# Test 5: Create Sample Orders
echo "📋 Test 5: Creating Sample Orders"

# First, add items to cart
curl -s -X POST "$BASE_URL/cart/add" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"productId\": \"$(echo $PRODUCT1_RESPONSE | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)\",
    \"quantity\": 1
  }" > /dev/null

curl -s -X POST "$BASE_URL/cart/add" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"productId\": \"$(echo $PRODUCT2_RESPONSE | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)\",
    \"quantity\": 2
  }" > /dev/null

# Create order
ORDER_RESPONSE=$(curl -s -X POST "$BASE_URL/orders" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "address": {
      "street": "123 Test Street",
      "city": "Mumbai",
      "state": "Maharashtra",
      "postalCode": "400001",
      "country": "India"
    },
    "paymentInfo": {
      "method": "cod"
    }
  }')

echo "Order creation response: $ORDER_RESPONSE"
echo ""

# Test 6: Create Sample Coupon
echo "📋 Test 6: Creating Sample Coupon"
COUPON_RESPONSE=$(curl -s -X POST "$BASE_URL/coupons" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "WELCOME20",
    "description": "Welcome discount - 20% off on first order",
    "discountType": "percentage",
    "discountValue": 20,
    "minimumOrderAmount": 500,
    "validFrom": "2024-01-01T00:00:00.000Z",
    "validUntil": "2024-12-31T23:59:59.999Z",
    "usageLimit": 1000,
    "isActive": true
  }')

echo "Coupon creation response: $COUPON_RESPONSE"
echo ""

# Test 7: Verify Data for Admin Panel
echo "📋 Test 7: Verifying Data for Admin Panel"

echo "Categories:"
curl -s -X GET "$BASE_URL/categories" | head -c 200
echo -e "\n"

echo "Products:"
curl -s -X GET "$BASE_URL/products" | head -c 200
echo -e "\n"

echo "Orders (Admin view):"
curl -s -X GET "$BASE_URL/orders" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | head -c 200
echo -e "\n"

echo "Dashboard Stats:"
curl -s -X GET "$BASE_URL/admin/stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | head -c 200
echo -e "\n"

echo "Coupons:"
curl -s -X GET "$BASE_URL/coupons" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | head -c 200
echo -e "\n"

echo "🎉 Test Data Created Successfully!"
echo "=================================="
echo ""
echo "🔑 Admin Login Credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123"
echo ""
echo "📊 Sample Data Created:"
echo "✅ 3 Categories (Electronics, Clothing, Home & Garden)"
echo "✅ 3 Products (Smartphone, T-Shirt, Garden Shovel)"
echo "✅ 1 Order from regular user"
echo "✅ 1 Coupon (WELCOME20)"
echo "✅ 1 Regular user (<EMAIL>)"
echo ""
echo "🌐 Admin Panel: http://localhost:3001"
echo "🔧 Backend API: http://localhost:8080"
echo ""
echo "Now you can:"
echo "1. Open http://localhost:3001 in your browser"
echo "2. <NAME_EMAIL> / admin123"
echo "3. See real data from the database"
echo "4. Add/edit/delete products and categories"
echo "5. View orders and manage inventory"
