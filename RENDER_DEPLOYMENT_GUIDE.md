# 🚀 Render Deployment Guide - Ghanshyam Murti Bhandar Backend

## 📋 Prerequisites

1. **GitHub Repository** - Your code should be pushed to GitHub
2. **Render Account** - Sign up at [render.com](https://render.com)
3. **MongoDB Atlas** - Cloud MongoDB database (free tier available)

---

## 🗄️ Step 1: Setup MongoDB Atlas

### 1.1 Create MongoDB Atlas Account
1. Go to [mongodb.com/atlas](https://mongodb.com/atlas)
2. Sign up for free account
3. Create a new project: "Ghanshyam Murti Bhandar"

### 1.2 Create Database Cluster
1. Click "Build a Database"
2. Choose **FREE** tier (M0 Sandbox)
3. Select **AWS** provider
4. Choose region closest to your users (e.g., Mumbai for India)
5. Cluster Name: `ghanshyam-cluster`
6. Click "Create Cluster"

### 1.3 Setup Database Access
1. Go to "Database Access" in left sidebar
2. Click "Add New Database User"
3. Username: `ghanshyam-admin`
4. Password: Generate secure password (save it!)
5. Database User Privileges: "Read and write to any database"
6. Click "Add User"

### 1.4 Setup Network Access
1. Go to "Network Access" in left sidebar
2. Click "Add IP Address"
3. Click "Allow Access from Anywhere" (0.0.0.0/0)
4. Comment: "Render deployment"
5. Click "Confirm"

### 1.5 Get Connection String
1. Go to "Database" → "Connect"
2. Choose "Connect your application"
3. Driver: Node.js, Version: 4.1 or later
4. Copy connection string:
   ```
   mongodb+srv://ghanshyam-admin:<password>@ghanshyam-cluster.xxxxx.mongodb.net/?retryWrites=true&w=majority
   ```
5. Replace `<password>` with your actual password
6. Add database name: `/ghanshyam_db`

---

## 🌐 Step 2: Deploy Backend to Render

### 2.1 Prepare Your Repository
1. Ensure your `App_Backend` folder is in the root of your GitHub repo
2. Create `.gitignore` in `App_Backend` if not exists:
   ```
   node_modules/
   .env
   uploads/
   *.log
   ```

### 2.2 Update package.json
Ensure your `App_Backend/package.json` has:
```json
{
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
```

### 2.3 Create Render Web Service
1. Login to [render.com](https://render.com)
2. Click "New +" → "Web Service"
3. Connect your GitHub repository
4. Configure service:

**Basic Settings:**
- **Name**: `ghanshyam-backend`
- **Region**: Singapore (closest to India)
- **Branch**: `main`
- **Root Directory**: `App_Backend`
- **Runtime**: Node
- **Build Command**: `npm install`
- **Start Command**: `npm start`

**Advanced Settings:**
- **Instance Type**: Free (for testing) or Starter ($7/month for production)
- **Auto-Deploy**: Yes

### 2.4 Environment Variables
Add these environment variables in Render dashboard:

```bash
NODE_ENV=production
PORT=10000
MONGO_URI=mongodb+srv://ghanshyam-admin:<EMAIL>/ghanshyam_db?retryWrites=true&w=majority
JWT_SECRET=your-super-secret-jwt-key-min-32-characters-long
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
ALLOWED_ORIGINS=https://your-admin-panel-url.onrender.com,https://your-frontend-url.onrender.com
```

**Important Notes:**
- Replace `YOUR_PASSWORD` with your MongoDB password
- Generate a strong JWT_SECRET (32+ characters)
- Add your actual Razorpay credentials
- Update ALLOWED_ORIGINS with your frontend URLs

### 2.5 Deploy
1. Click "Create Web Service"
2. Render will automatically build and deploy
3. Wait for deployment to complete (5-10 minutes)
4. Your API will be available at: `https://ghanshyam-backend.onrender.com`

---

## 🎯 Step 3: Deploy Admin Panel to Render

### 3.1 Update Admin Panel Configuration
In `Application_Admin/lib/api.js`, update the base URL:
```javascript
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://ghanshyam-backend.onrender.com/api'
  : 'http://localhost:8080/api';
```

### 3.2 Create Admin Panel Service
1. In Render dashboard, click "New +" → "Static Site"
2. Connect your GitHub repository
3. Configure:

**Settings:**
- **Name**: `ghanshyam-admin`
- **Branch**: `main`
- **Root Directory**: `Application_Admin`
- **Build Command**: `npm install && npm run build`
- **Publish Directory**: `out` (for Next.js static export)

### 3.3 Configure Next.js for Static Export
Add to `Application_Admin/next.config.js`:
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

module.exports = nextConfig
```

### 3.4 Update package.json
Add export script to `Application_Admin/package.json`:
```json
{
  "scripts": {
    "build": "next build && next export"
  }
}
```

---

## ✅ Step 4: Verification & Testing

### 4.1 Test Backend API
```bash
# Health check
curl https://ghanshyam-backend.onrender.com/health

# Test login
curl -X POST https://ghanshyam-backend.onrender.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### 4.2 Test Admin Panel
1. Visit: `https://ghanshyam-admin.onrender.com`
2. Login with admin credentials
3. Verify all features work

### 4.3 Update CORS Settings
If you get CORS errors, update `ALLOWED_ORIGINS` in Render environment variables:
```
ALLOWED_ORIGINS=https://ghanshyam-admin.onrender.com,https://your-other-frontend.onrender.com
```

---

## 🔧 Step 5: Production Optimizations

### 5.1 Enable Logging
Add to your `app.js`:
```javascript
// Production logging
if (process.env.NODE_ENV === 'production') {
    app.use(morgan('combined'));
} else {
    app.use(morgan('dev'));
}
```

### 5.2 Security Headers
Your helmet configuration is already good, but ensure:
```javascript
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
```

### 5.3 Rate Limiting
Update rate limiting for production:
```javascript
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.NODE_ENV === 'production' ? 100 : 1000,
    message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
    }
});
```

---

## 📊 Step 6: Monitoring & Maintenance

### 6.1 Render Dashboard
- Monitor deployment logs
- Check service health
- View metrics and usage

### 6.2 MongoDB Atlas Monitoring
- Monitor database performance
- Set up alerts for high usage
- Regular backups (automatic in Atlas)

### 6.3 Custom Domain (Optional)
1. In Render dashboard → Settings → Custom Domains
2. Add your domain: `api.ghanshyammurtibhandar.com`
3. Update DNS records as instructed
4. SSL certificate will be auto-generated

---

## 🚨 Troubleshooting

### Common Issues:

**1. Build Fails:**
- Check Node.js version compatibility
- Verify all dependencies in package.json
- Check build logs in Render dashboard

**2. Database Connection Fails:**
- Verify MongoDB connection string
- Check network access settings in Atlas
- Ensure password is URL-encoded

**3. CORS Errors:**
- Update ALLOWED_ORIGINS environment variable
- Restart the service after changes

**4. File Upload Issues:**
- Render has ephemeral storage
- Consider using cloud storage (AWS S3, Cloudinary)

---

## 💰 Cost Estimation

### Free Tier:
- **Render Web Service**: Free (with limitations)
- **MongoDB Atlas**: Free M0 cluster
- **Total**: $0/month

### Production Tier:
- **Render Web Service**: $7/month (Starter)
- **MongoDB Atlas**: $9/month (M10)
- **Total**: $16/month

---

## 🎉 Deployment Complete!

Your backend will be available at:
- **API**: `https://ghanshyam-backend.onrender.com/api`
- **Admin Panel**: `https://ghanshyam-admin.onrender.com`
- **Health Check**: `https://ghanshyam-backend.onrender.com/health`

**Next Steps:**
1. Update your Flutter app to use the production API URL
2. Test all functionality thoroughly
3. Set up monitoring and alerts
4. Consider upgrading to paid plans for production use

🚀 **Your ecommerce platform is now live and ready for customers!**
