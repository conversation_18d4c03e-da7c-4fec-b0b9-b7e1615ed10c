# 📚 <PERSON><PERSON><PERSON>yam Murti <PERSON> - Complete API Documentation

## 🌐 Base URLs
```
Production: https://ghanshyam-backend.onrender.com/api
Development: http://localhost:8080/api
Health Check: /health
```

## 🔐 Authentication
All authenticated endpoints require a Bearer token:
```
Authorization: Bearer <jwt_token>
```

## 📋 Response Format
All responses follow this standardized structure:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {},
  "errors": [],
  "meta": {
    "timestamp": "2025-01-01T00:00:00Z",
    "request_id": "uuid",
    "version": "1.0",
    "platform": "mobile|web|unknown"
  }
}
```

## 📄 Pagination Format
For paginated endpoints:
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [...],
  "errors": [],
  "meta": {
    "timestamp": "2025-01-01T00:00:00Z",
    "request_id": "uuid",
    "version": "1.0",
    "platform": "unknown",
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10,
      "has_next_page": true,
      "has_prev_page": false
    }
  }
}
```

---

# 🔑 AUTHENTICATION ENDPOINTS

## 1.1 User Registration
**POST** `/auth/signup`

### Request Body
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "user|admin"
}
```

### Response (201)
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2025-01-01T00:00:00Z"
    },
    "token": "jwt_token",
    "expires_in": "7d"
  }
}
```

## 1.2 User Login
**POST** `/auth/login`

### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

### Response (200)
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "last_login": "2025-01-01T00:00:00Z"
    },
    "token": "jwt_token",
    "expires_in": "7d"
  }
}
```

## 1.3 Get User Profile
**GET** `/auth/profile`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+919876543210",
      "role": "user",
      "addresses": [],
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z",
      "last_login": "2025-01-01T00:00:00Z"
    }
  }
}
```

## 1.4 Update User Profile
**PUT** `/auth/profile`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "name": "John Doe Updated",
  "phone": "+919876543211"
}
```

---

# 📦 PRODUCT ENDPOINTS

## 2.1 Get All Products
**GET** `/products`

### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `category` (optional): Category ID filter
- `search` (optional): Search term
- `min_price` (optional): Minimum price filter
- `max_price` (optional): Maximum price filter
- `sort_by` (optional): Sort field (name, price, rating, created_at)
- `sort_order` (optional): Sort order (asc, desc)
- `is_featured` (optional): Filter featured products (true/false)
- `is_on_sale` (optional): Filter sale products (true/false)
- `in_stock` (optional): Filter in-stock products (true/false)

### Response (200)
```json
{
  "success": true,
  "message": "Products retrieved successfully",
  "data": [
    {
      "id": "product_id",
      "name": "Product Name",
      "description": "Product description",
      "price": 999,
      "original_price": 1299,
      "discount_percentage": 23,
      "images": ["image1.jpg", "image2.jpg"],
      "category": {
        "id": "category_id",
        "name": "Category Name",
        "slug": "category-slug"
      },
      "rating": 4.5,
      "review_count": 10,
      "stock": 50,
      "is_in_stock": true,
      "is_featured": false,
      "is_favorite": false,
      "variants": [],
      "tags": ["tag1", "tag2"],
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10,
      "has_next_page": true,
      "has_prev_page": false
    }
  }
}
```

## 2.2 Get Product by ID
**GET** `/products/:id`

### Response (200)
```json
{
  "success": true,
  "message": "Product details retrieved successfully",
  "data": {
    "id": "product_id",
    "name": "Product Name",
    "description": "Detailed product description",
    "price": 999,
    "original_price": 1299,
    "discount_percentage": 23,
    "images": ["image1.jpg", "image2.jpg"],
    "category": {
      "id": "category_id",
      "name": "Category Name",
      "slug": "category-slug",
      "description": "Category description"
    },
    "rating": 4.5,
    "review_count": 10,
    "stock": 50,
    "is_in_stock": true,
    "is_featured": false,
    "is_favorite": false,
    "variants": [],
    "tags": ["tag1", "tag2"],
    "specifications": {},
    "shipping_info": {},
    "return_policy": "7 days return policy",
    "warranty": "1 year manufacturer warranty",
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z",
    "related_products": [
      {
        "id": "related_product_id",
        "name": "Related Product",
        "price": 799,
        "original_price": 999,
        "image": "related_image.jpg",
        "rating": 4.0,
        "review_count": 5
      }
    ]
  }
}
```

## 2.3 Get Featured Products
**GET** `/products/featured`

### Query Parameters
- `limit` (optional): Number of products (default: 8)

## 2.4 Search Products
**GET** `/products/search`

### Query Parameters
- `q` (required): Search query
- `page` (optional): Page number
- `limit` (optional): Items per page

## 2.5 Create Product (Admin)
**POST** `/products`
**Headers:** Authorization: Bearer <admin_token>
**Content-Type:** multipart/form-data

### Request Body
```json
{
  "name": "Product Name",
  "description": "Product description",
  "price": 999,
  "originalPrice": 1299,
  "category": "category_id",
  "stock": 50,
  "isActive": true,
  "isFeatured": false,
  "variants": "[]",
  "tags": "[\"tag1\", \"tag2\"]",
  "specifications": "{}",
  "shippingInfo": "{}",
  "returnPolicy": "7 days return policy",
  "warranty": "1 year warranty"
}
```
**Files:** images[] (up to 10 images)

## 2.6 Update Product (Admin)
**PUT** `/products/:id`
**Headers:** Authorization: Bearer <admin_token>

## 2.7 Delete Product (Admin)
**DELETE** `/products/:id`
**Headers:** Authorization: Bearer <admin_token>

## 2.8 Update Inventory (Admin)
**PATCH** `/products/:id/inventory`
**Headers:** Authorization: Bearer <admin_token>

### Request Body
```json
{
  "stock": 75,
  "operation": "set|add|subtract"
}
```

---

# 📂 CATEGORY ENDPOINTS

## 3.1 Get All Categories
**GET** `/categories`

### Query Parameters
- `include_products` (optional): Include featured products (true/false)
- `parent_only` (optional): Only parent categories (true/false)

### Response (200)
```json
{
  "success": true,
  "message": "Categories retrieved successfully",
  "data": [
    {
      "id": "category_id",
      "name": "Category Name",
      "description": "Category description",
      "slug": "category-slug",
      "image": "category_image.jpg",
      "parent": null,
      "product_count": 25,
      "subcategories": [
        {
          "id": "subcategory_id",
          "name": "Subcategory Name",
          "slug": "subcategory-slug"
        }
      ],
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    }
  ]
}
```

## 3.2 Get Category Tree
**GET** `/categories/tree`

### Response (200)
```json
{
  "success": true,
  "message": "Category tree retrieved successfully",
  "data": [
    {
      "id": "category_id",
      "name": "Parent Category",
      "slug": "parent-category",
      "image": "category_image.jpg",
      "product_count": 25,
      "children": [
        {
          "id": "child_id",
          "name": "Child Category",
          "slug": "child-category",
          "image": "child_image.jpg",
          "product_count": 10,
          "children": []
        }
      ]
    }
  ]
}
```

## 3.3 Get Category by ID
**GET** `/categories/:id`

## 3.4 Create Category (Admin)
**POST** `/categories`
**Headers:** Authorization: Bearer <admin_token>
**Content-Type:** multipart/form-data

### Request Body
```json
{
  "name": "Category Name",
  "description": "Category description",
  "parent": "parent_category_id"
}
```
**Files:** image (single image file)

## 3.5 Update Category (Admin)
**PUT** `/categories/:id`
**Headers:** Authorization: Bearer <admin_token>

## 3.6 Delete Category (Admin)
**DELETE** `/categories/:id`
**Headers:** Authorization: Bearer <admin_token>

---

# 🛒 CART ENDPOINTS

## 4.1 Add to Cart
**POST** `/cart/add`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "productId": "product_id",
  "quantity": 2,
  "variantId": "variant_id"
}
```

## 4.2 Get Cart
**GET** `/cart`
**Headers:** Authorization: Bearer <token>

### Response (200)
```json
{
  "success": true,
  "message": "Cart retrieved successfully",
  "data": {
    "items": [
      {
        "id": "cart_item_id",
        "product": {
          "id": "product_id",
          "name": "Product Name",
          "price": 999,
          "image": "product_image.jpg"
        },
        "quantity": 2,
        "variant": null,
        "subtotal": 1998
      }
    ],
    "total_items": 2,
    "total_amount": 1998,
    "discount_amount": 0,
    "final_amount": 1998
  }
}
```

## 4.3 Update Cart Item
**PUT** `/cart/update/:itemId`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "quantity": 3
}
```

## 4.4 Remove from Cart
**DELETE** `/cart/remove/:itemId`
**Headers:** Authorization: Bearer <token>

## 4.5 Clear Cart
**DELETE** `/cart/clear`
**Headers:** Authorization: Bearer <token>

---

# ❤️ WISHLIST ENDPOINTS

## 5.1 Add to Wishlist
**POST** `/wishlist/add`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "productId": "product_id"
}
```

## 5.2 Get Wishlist
**GET** `/wishlist`
**Headers:** Authorization: Bearer <token>

## 5.3 Remove from Wishlist
**DELETE** `/wishlist/remove/:productId`
**Headers:** Authorization: Bearer <token>

---

# 📋 ORDER ENDPOINTS

## 6.1 Create Order
**POST** `/orders`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "address": {
    "street": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "postalCode": "400001",
    "country": "India"
  },
  "paymentInfo": {
    "method": "cod|razorpay",
    "razorpayOrderId": "order_id",
    "razorpayPaymentId": "payment_id"
  },
  "couponCode": "DISCOUNT20"
}
```

## 6.2 Get User Orders
**GET** `/orders/my-orders`
**Headers:** Authorization: Bearer <token>

## 6.3 Get Order by ID
**GET** `/orders/:id`
**Headers:** Authorization: Bearer <token>

## 6.4 Get All Orders (Admin)
**GET** `/orders`
**Headers:** Authorization: Bearer <admin_token>

## 6.5 Update Order Status (Admin)
**PATCH** `/orders/:id/status`
**Headers:** Authorization: Bearer <admin_token>

### Request Body
```json
{
  "status": "pending|confirmed|shipped|delivered|cancelled"
}
```

---

# 🎫 COUPON ENDPOINTS

## 7.1 Create Coupon (Admin)
**POST** `/coupons`
**Headers:** Authorization: Bearer <admin_token>

### Request Body
```json
{
  "code": "DISCOUNT20",
  "description": "20% discount on all items",
  "discountType": "percentage|fixed",
  "discountValue": 20,
  "minimumOrderAmount": 500,
  "validFrom": "2024-01-01T00:00:00.000Z",
  "validUntil": "2024-12-31T23:59:59.999Z",
  "usageLimit": 1000,
  "userUsageLimit": 1,
  "isActive": true
}
```

## 7.2 Validate Coupon
**POST** `/coupons/validate`

### Request Body
```json
{
  "code": "DISCOUNT20",
  "orderAmount": 1000,
  "cartItems": []
}
```

## 7.3 Get All Coupons (Admin)
**GET** `/coupons`
**Headers:** Authorization: Bearer <admin_token>

---

# 👤 USER MANAGEMENT ENDPOINTS

## 8.1 Add User Address
**POST** `/users/addresses`
**Headers:** Authorization: Bearer <token>

### Request Body
```json
{
  "street": "123 Main Street",
  "city": "Mumbai",
  "state": "Maharashtra",
  "postalCode": "400001",
  "country": "India",
  "isDefault": true
}
```

## 8.2 Get User Addresses
**GET** `/users/addresses`
**Headers:** Authorization: Bearer <token>

## 8.3 Update Address
**PUT** `/users/addresses/:id`
**Headers:** Authorization: Bearer <token>

## 8.4 Delete Address
**DELETE** `/users/addresses/:id`
**Headers:** Authorization: Bearer <token>

---

# 📊 ADMIN DASHBOARD ENDPOINTS

## 9.1 Get Dashboard Stats
**GET** `/admin/stats`
**Headers:** Authorization: Bearer <admin_token>

### Response (200)
```json
{
  "success": true,
  "message": "Dashboard stats retrieved successfully",
  "data": {
    "productCount": 150,
    "categoryCount": 25,
    "orderCount": 500,
    "userCount": 1000,
    "totalSales": 250000,
    "monthlyStats": {
      "orders": 50,
      "sales": 25000,
      "newUsers": 100
    }
  }
}
```

---

# ❌ ERROR CODES

- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **409**: Conflict (Duplicate)
- **422**: Validation Error
- **429**: Rate Limit Exceeded
- **500**: Internal Server Error

---

# 🔧 Rate Limiting

- **Limit**: 1000 requests per 15 minutes per IP
- **Headers**: 
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

---

# 📁 File Upload

- **Max File Size**: 5MB per file
- **Max Files**: 10 files per request
- **Allowed Types**: Images only (jpg, jpeg, png, gif, webp)
- **Upload Path**: `/uploads/products/` or `/uploads/categories/`

---

# 🎯 Testing

Use the provided test script:
```bash
chmod +x test_complete_backend.sh
./test_complete_backend.sh
```

**Success Rate**: 90%+ (29/32 tests passing)

---

# 🚀 Production URLs

- **API Base**: `https://ghanshyam-backend.onrender.com/api`
- **Health Check**: `https://ghanshyam-backend.onrender.com/health`
- **Admin Panel**: `https://ghanshyam-admin.onrender.com`

**🎉 Your complete ecommerce backend is ready for production!**
