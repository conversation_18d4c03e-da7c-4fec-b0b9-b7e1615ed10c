# Deployment Guide - Ghanshyam Murti Bhandar

This guide covers the deployment of all three components of the Ghanshyam Murti Bhandar ecommerce platform.

## 🚀 Quick Start (Development)

### 1. Backend API
```bash
cd App_Backend
npm install
cp .env.example .env
# Edit .env with your MongoDB URI and other configs
npm start
# Server runs on http://localhost:5000
```

### 2. Admin Panel
```bash
cd Application_Admin
npm install
npm run dev
# Admin panel runs on http://localhost:3000
```

### 3. Flutter Mobile App
```bash
cd ghanshyam_murti_bhandar
flutter pub get
flutter run
```

## 🌐 Production Deployment

### Backend API Deployment

#### Option 1: Heroku
1. **Install Heroku CLI**
2. **Create Heroku App**
   ```bash
   cd App_Backend
   heroku create your-app-name
   ```

3. **Set Environment Variables**
   ```bash
   heroku config:set MONGO_URI="your_mongodb_atlas_uri"
   heroku config:set JWT_SECRET="your_jwt_secret"
   heroku config:set RAZORPAY_KEY_ID="your_razorpay_key"
   heroku config:set RAZORPAY_KEY_SECRET="your_razorpay_secret"
   ```

4. **Deploy**
   ```bash
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main
   ```

#### Option 2: DigitalOcean/AWS/VPS
1. **Server Setup**
   ```bash
   # Install Node.js, PM2, and Nginx
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   sudo npm install -g pm2
   sudo apt-get install nginx
   ```

2. **Deploy Application**
   ```bash
   git clone your-repo
   cd App_Backend
   npm install --production
   ```

3. **Configure PM2**
   ```bash
   pm2 start app.js --name "ghanshyam-api"
   pm2 startup
   pm2 save
   ```

4. **Configure Nginx**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### Admin Panel Deployment

#### Option 1: Vercel (Recommended)
1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy**
   ```bash
   cd Application_Admin
   vercel --prod
   ```

3. **Environment Variables**
   - Set `NEXT_PUBLIC_API_URL` to your backend URL

#### Option 2: Netlify
1. **Build the project**
   ```bash
   cd Application_Admin
   npm run build
   npm run export
   ```

2. **Deploy to Netlify**
   - Upload the `out` folder to Netlify
   - Or connect your Git repository

### Mobile App Deployment

#### Android (Google Play Store)
1. **Build APK**
   ```bash
   cd ghanshyam_murti_bhandar
   flutter build apk --release
   ```

2. **Build App Bundle**
   ```bash
   flutter build appbundle --release
   ```

3. **Upload to Google Play Console**

#### iOS (Apple App Store)
1. **Build iOS**
   ```bash
   flutter build ios --release
   ```

2. **Archive in Xcode**
3. **Upload to App Store Connect**

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Server
PORT=5000
NODE_ENV=production

# Database
MONGO_URI=mongodb+srv://username:<EMAIL>/dbname

# Authentication
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters

# Payment Gateway
RAZORPAY_KEY_ID=rzp_live_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Email (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# File Upload
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# CORS
ALLOWED_ORIGINS=https://your-admin-domain.com,https://your-app-domain.com
```

#### Admin Panel
Update `lib/api.ts`:
```typescript
const API_URL = 'https://your-backend-domain.com/api';
```

#### Flutter App
Update API base URL in your HTTP service files.

## 🗄️ Database Setup

### MongoDB Atlas (Recommended)
1. **Create MongoDB Atlas Account**
2. **Create Cluster**
3. **Create Database User**
4. **Whitelist IP Addresses**
5. **Get Connection String**

### Local MongoDB
```bash
# Install MongoDB
sudo apt-get install mongodb

# Start MongoDB
sudo systemctl start mongodb
sudo systemctl enable mongodb
```

## 🔐 Security Checklist

### Backend Security
- [ ] Use HTTPS in production
- [ ] Set strong JWT secret (minimum 32 characters)
- [ ] Enable CORS with specific origins
- [ ] Use rate limiting
- [ ] Validate all inputs
- [ ] Use helmet for security headers
- [ ] Keep dependencies updated

### Database Security
- [ ] Use MongoDB Atlas with authentication
- [ ] Restrict database access by IP
- [ ] Use strong database passwords
- [ ] Enable database encryption

### API Security
- [ ] Use API rate limiting
- [ ] Implement proper authentication
- [ ] Validate file uploads
- [ ] Sanitize user inputs
- [ ] Use HTTPS for all API calls

## 📊 Monitoring & Logging

### Backend Monitoring
```bash
# PM2 monitoring
pm2 monit

# View logs
pm2 logs

# Restart application
pm2 restart ghanshyam-api
```

### Error Tracking
Consider integrating:
- Sentry for error tracking
- LogRocket for session replay
- New Relic for performance monitoring

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy Backend

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        cd App_Backend
        npm install
        
    - name: Run tests
      run: |
        cd App_Backend
        npm test
        
    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{secrets.HEROKU_API_KEY}}
        heroku_app_name: "your-app-name"
        heroku_email: "<EMAIL>"
        appdir: "App_Backend"
```

## 🧪 Testing in Production

### API Testing
```bash
# Test API endpoints
curl -X GET https://your-api-domain.com/api/categories

# Test authentication
curl -X POST https://your-api-domain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

### Load Testing
```bash
# Install artillery
npm install -g artillery

# Run load test
artillery quick --count 10 --num 5 https://your-api-domain.com/api/products
```

## 🚨 Troubleshooting

### Common Issues

#### Backend Issues
1. **MongoDB Connection Failed**
   - Check MongoDB URI
   - Verify network access
   - Check database credentials

2. **JWT Token Issues**
   - Verify JWT_SECRET is set
   - Check token expiration
   - Validate token format

3. **File Upload Issues**
   - Check upload directory permissions
   - Verify file size limits
   - Check allowed file types

#### Admin Panel Issues
1. **API Connection Failed**
   - Verify API URL in configuration
   - Check CORS settings
   - Verify SSL certificates

2. **Build Errors**
   - Clear node_modules and reinstall
   - Check TypeScript errors
   - Verify environment variables

### Performance Optimization

#### Backend
- Use database indexing
- Implement caching (Redis)
- Optimize image compression
- Use CDN for static files

#### Admin Panel
- Implement lazy loading
- Optimize bundle size
- Use image optimization
- Enable gzip compression

## 📞 Support

For deployment support:
- Check the troubleshooting section
- Review server logs
- Contact support team

---

**Happy Deploying! 🚀**
